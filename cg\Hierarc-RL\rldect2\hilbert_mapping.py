"""
希尔伯特空间映射模块 - 使用残差增强型随机傅里叶特征实现高维特征映射到低维空间
"""
import torch
import torch.nn as nn
import math
import time
import numpy as np
import os

class RandomFourierFeatureMapper:
    """随机傅里叶特征映射器，用于高维特征的低维投影

    该类实现了随机傅里叶特征(RFF)映射方法，将高维输入特征映射到低维空间，
    同时保持特征之间的相似性关系。这种映射是高斯核的蒙特卡洛近似。
    """

    def __init__(self, input_dim, output_dim, gamma=1.0, seed=None):
        """初始化随机傅里叶特征映射器

        Args:
            input_dim: 输入特征维度
            output_dim: 输出特征维度，必须是偶数
            gamma: RBF核参数，控制频率分布的带宽
            seed: 随机种子，确保可重复性
        """
        if output_dim % 2 != 0:
            raise ValueError("输出维度必须是偶数")

        self.input_dim = input_dim
        self.output_dim = output_dim
        self.gamma = gamma

        # 设置随机种子
        if seed is not None:
            np.random.seed(seed)

        # 随机采样权重矩阵 W ~ N(0, 2*gamma*I)
        # 权重矩阵维度为 [input_dim, output_dim//2]
        self.weights = np.random.normal(
            0, np.sqrt(2 * gamma),
            size=(input_dim, output_dim // 2)
        )

        # 随机采样偏置 b ~ Uniform(0, 2*pi)
        self.offsets = np.random.uniform(
            0, 2 * np.pi,
            size=output_dim // 2
        )

        # 用于缩放输出
        self.scaling = np.sqrt(2.0 / output_dim)

        # 统计相关
        self.num_transforms = 0
        self.total_time = 0
        self.batch_count = 0
        self.batch_total_time = 0

    def transform(self, x):
        """将输入特征x映射到希尔伯特空间

        Args:
            x: 输入特征向量，形状为 [input_dim]

        Returns:
            映射后的特征向量，形状为 [output_dim]
        """
        start_time = time.time()

        # 确保x是一维向量
        if len(x.shape) > 1:
            raise ValueError("输入必须是一维向量")

        # 计算 W^T * x + b
        projection = np.dot(x, self.weights) + self.offsets

        # 应用三角函数变换
        # z = [cos(W^T * x + b), sin(W^T * x + b)]
        z_cos = np.cos(projection)
        z_sin = np.sin(projection)

        # 连接余弦和正弦部分
        z = np.concatenate([z_cos, z_sin]) * self.scaling

        # 更新统计信息
        self.num_transforms += 1
        self.total_time += time.time() - start_time

        return z

    def batch_transform(self, X, batch_size=64):
        """批量处理输入特征

        处理大批量数据时更有效率。

        Args:
            X: 输入特征矩阵，形状为 [num_samples, input_dim]
            batch_size: 批处理大小

        Returns:
            映射后的特征矩阵，形状为 [num_samples, output_dim]
        """
        start_time = time.time()

        # 确保X是二维矩阵
        if len(X.shape) != 2:
            raise ValueError("输入必须是二维矩阵 [num_samples, input_dim]")

        num_samples = X.shape[0]

        # 初始化结果矩阵
        Z = np.zeros((num_samples, self.output_dim))

        # 批量处理
        for i in range(0, num_samples, batch_size):
            end = min(i + batch_size, num_samples)
            batch_X = X[i:end]

            # 计算 X * W + b
            projection = np.dot(batch_X, self.weights) + self.offsets

            # 应用三角函数变换
            Z[i:end, :self.output_dim//2] = np.cos(projection) * self.scaling
            Z[i:end, self.output_dim//2:] = np.sin(projection) * self.scaling

        # 更新统计信息
        self.batch_count += 1
        self.batch_total_time += time.time() - start_time

        return Z

    def get_stats(self):
        """获取映射统计信息

        Returns:
            包含统计信息的字典
        """
        avg_transform_time = 0
        if self.num_transforms > 0:
            avg_transform_time = self.total_time / self.num_transforms

        avg_batch_time = 0
        if self.batch_count > 0:
            avg_batch_time = self.batch_total_time / self.batch_count

        return {
            "num_transforms": self.num_transforms,
            "avg_transform_time": avg_transform_time * 1000,  # 转换为毫秒
            "batch_count": self.batch_count,
            "avg_batch_time": avg_batch_time * 1000  # 转换为毫秒
        }


class HilbertMappingLayer(nn.Module):
    """希尔伯特空间映射层，用于PyTorch模型中的随机傅里叶特征变换

    这个层使用随机傅里叶特征将高维输入特征映射到低维空间，同时保持相似度关系。
    可以作为深度学习模型的预处理层使用，用于降低计算复杂度。
    """

    def __init__(self, input_dim, output_dim, gamma=1.0, seed=None):
        """初始化希尔伯特空间映射层

        Args:
            input_dim: 输入特征维度
            output_dim: 输出特征维度，必须是偶数
            gamma: RBF核参数，控制频率分布的带宽
            seed: 随机种子，确保可重复性
        """
        super(HilbertMappingLayer, self).__init__()

        # 确保输出维度是偶数
        if output_dim % 2 != 0:
            raise ValueError("输出维度必须是偶数")

        # 初始化参数
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.gamma = gamma

        # 设置随机种子
        if seed is not None:
            torch.manual_seed(seed)

        # 随机权重矩阵，需要梯度
        self.weights = nn.Parameter(
            torch.randn(input_dim, output_dim // 2) * math.sqrt(2 * gamma),
            requires_grad=False  # 设为False表示不参与训练
        )

        # 随机偏置，需要梯度
        self.offsets = nn.Parameter(
            torch.rand(output_dim // 2) * (2 * math.pi),
            requires_grad=False  # 设为False表示不参与训练
        )

        # 缩放因子
        self.scaling = math.sqrt(2.0 / output_dim)

        # 统计相关
        self.forward_count = 0
        self.total_time = 0

    def forward(self, x):
        """前向传播，应用随机傅里叶特征变换

        Args:
            x: 输入tensor，形状为 [batch_size, input_dim]

        Returns:
            映射后的特征tensor，形状为 [batch_size, output_dim]
        """
        start_time = time.time()

        # 计算 x * W
        projection = torch.matmul(x, self.weights)

        # 添加偏置
        projection = projection + self.offsets

        # 计算cos和sin，并缩放
        z_cos = torch.cos(projection) * self.scaling
        z_sin = torch.sin(projection) * self.scaling

        # 在最后一个维度上拼接
        z = torch.cat([z_cos, z_sin], dim=-1)

        # 更新统计信息
        self.forward_count += 1
        self.total_time += time.time() - start_time

        return z

    def get_stats(self):
        """获取层的统计信息

        Returns:
            包含统计信息的字典
        """
        avg_forward_time = 0
        if self.forward_count > 0:
            avg_forward_time = self.total_time / self.forward_count

        return {
            "forward_count": self.forward_count,
            "avg_forward_time": avg_forward_time * 1000,  # 转换为毫秒
            "input_dim": self.input_dim,
            "output_dim": self.output_dim,
            "reduction_ratio": self.output_dim / self.input_dim
        }


class ResidualEnhancedHilbertMapping(nn.Module):
    """残差增强型希尔伯特空间映射层

    通过特征感知设计和残差连接，在保持低计算成本的同时提升映射质量。
    结合特征分类处理和自适应频率重要性，为不同类型的特征提供最佳映射。
    """

    def __init__(self, input_dim, output_dim, gamma=0.01, residual_ratio=0.1):
        super().__init__()

        # 基本参数
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.base_gamma = gamma
        self.residual_ratio = residual_ratio

        # 确保输出维度是偶数
        if output_dim % 2 != 0:
            output_dim += 1

        # 特征分组 - 假设最后10维是几何特征
        self.feature_dim = input_dim - 98

        # 计算各部分输出维度
        img_output_dim = int(output_dim * 0.9)
        if img_output_dim % 2 != 0:
            img_output_dim -= 1

        geo_output_dim = output_dim - img_output_dim
        if geo_output_dim % 2 != 0:
            geo_output_dim += 1
            img_output_dim -= 1

        self.img_output_dim = img_output_dim
        self.geo_output_dim = geo_output_dim

        # 图像特征映射 - 使用较小的gamma
        self.img_weights = nn.Parameter(
            torch.randn(self.feature_dim, img_output_dim // 2) * math.sqrt(2 * gamma * 0.5),
            requires_grad=False
        )
        self.img_offsets = nn.Parameter(
            torch.rand(img_output_dim // 2) * (2 * math.pi),
            requires_grad=False
        )

        # 几何特征映射 - 使用较大的gamma
        self.geo_weights = nn.Parameter(
            torch.randn(98, geo_output_dim // 2) * math.sqrt(2 * gamma * 2.0),
            requires_grad=False
        )
        self.geo_offsets = nn.Parameter(
            torch.rand(geo_output_dim // 2) * (2 * math.pi),
            requires_grad=False
        )

        # 特征重要性权重
        self.feature_weights = nn.Parameter(
            torch.ones(input_dim),
            requires_grad=True
        )

        # 频率重要性
        self.img_frequency_importance = nn.Parameter(
            torch.ones(img_output_dim // 2),
            requires_grad=True
        )
        self.geo_frequency_importance = nn.Parameter(
            torch.ones(geo_output_dim // 2),
            requires_grad=True
        )

        # 缩放因子
        self.scaling = math.sqrt(2.0 / output_dim)

        # 残差连接部分
        self.residual_dim = max(int(output_dim * residual_ratio), 4)
        if self.residual_dim % 2 != 0:
            self.residual_dim += 1

        # 压缩原始特征的投影层
        self.residual_compressor = nn.Sequential(
            nn.Linear(input_dim, self.residual_dim),
            nn.LayerNorm(self.residual_dim),
            nn.LeakyReLU(0.1)
        )

        # 门控机制 - 控制残差信息的流动
        self.residual_gate = nn.Parameter(torch.tensor(0.5), requires_grad=True)

        # 最终融合层 - 将映射结果和残差特征融合
        self.fusion_layer = nn.Sequential(
            nn.Linear(output_dim + self.residual_dim, output_dim),
            nn.LayerNorm(output_dim)
        )

        # 性能监控指标
        self.register_buffer('total_mapping_time', torch.tensor(0.0))
        self.register_buffer('total_residual_time', torch.tensor(0.0))
        self.register_buffer('forward_count', torch.tensor(0))

    def flops_estimate(self, batch_size=1):
        """估算模块的FLOPs"""
        # 主映射部分FLOPs
        img_mapping_flops = self.feature_dim * (self.img_output_dim // 2)  # 矩阵乘法
        geo_mapping_flops = 10 * (self.geo_output_dim // 2)  # 矩阵乘法

        feature_weighting_flops = self.input_dim  # 特征权重相乘

        # 余弦/正弦计算 (估计每个三角函数5个FLOPs)
        trig_flops = ((self.img_output_dim // 2) + (self.geo_output_dim // 2)) * 2 * 5

        # 残差部分FLOPs
        residual_projection_flops = self.input_dim * self.residual_dim  # 线性层
        residual_ln_flops = self.residual_dim * 3  # 层归一化(均值、方差、标准化)
        residual_activation_flops = self.residual_dim  # LeakyReLU

        # 融合部分FLOPs
        if self.training:
            fusion_flops = (self.output_dim + self.residual_dim) * self.output_dim  # 融合层
            fusion_ln_flops = self.output_dim * 3  # 层归一化
        else:
            # 推理时使用简化融合
            fusion_flops = self.output_dim * 2  # 直接缩放和加法

        # 计算总FLOPs
        main_mapping_flops = img_mapping_flops + geo_mapping_flops + feature_weighting_flops + trig_flops
        residual_flops = residual_projection_flops + residual_ln_flops + residual_activation_flops

        if self.training:
            fusion_total_flops = fusion_flops + fusion_ln_flops
        else:
            fusion_total_flops = fusion_flops

        total_flops = main_mapping_flops + residual_flops + fusion_total_flops

        # 批量FLOPs
        batch_flops = total_flops * batch_size

        flops_breakdown = {
            'main_mapping': main_mapping_flops * batch_size,
            'residual': residual_flops * batch_size,
            'fusion': fusion_total_flops * batch_size,
            'total': batch_flops,
            'batch_size': batch_size
        }

        return flops_breakdown

    def forward(self, x):
        # 记录开始时间
        start_time = time.time()

        # 特征分组
        img_features = x[:, :self.feature_dim]
        geo_features = x[:, self.feature_dim:]

        # 应用特征重要性权重
        feature_weights = torch.sigmoid(self.feature_weights)
        weighted_img = img_features * feature_weights[:self.feature_dim]
        weighted_geo = geo_features * feature_weights[self.feature_dim:]

        # 应用映射 - 图像特征
        img_projection = torch.matmul(weighted_img, self.img_weights)
        img_projection = img_projection + self.img_offsets

        # 应用映射 - 几何特征
        geo_projection = torch.matmul(weighted_geo, self.geo_weights)
        geo_projection = geo_projection + self.geo_offsets

        # 应用频率重要性
        if not self.training:
            img_freq_weights = torch.sigmoid(self.img_frequency_importance)
            geo_freq_weights = torch.sigmoid(self.geo_frequency_importance)
            img_projection = img_projection * img_freq_weights
            geo_projection = geo_projection * geo_freq_weights

        # 计算映射输出
        img_cos = torch.cos(img_projection) * self.scaling
        img_sin = torch.sin(img_projection) * self.scaling
        img_output = torch.cat([img_cos, img_sin], dim=-1)

        geo_cos = torch.cos(geo_projection) * self.scaling
        geo_sin = torch.sin(geo_projection) * self.scaling
        geo_output = torch.cat([geo_cos, geo_sin], dim=-1)

        # 合并主映射输出
        primary_output = torch.cat([img_output, geo_output], dim=-1)

        # 记录映射时间
        mapping_time = time.time() - start_time
        self.total_mapping_time += mapping_time

        # 残差连接部分
        residual_start = time.time()

        # 计算残差分支
        residual_features = self.residual_compressor(x)

        # 应用门控机制
        gate_value = torch.sigmoid(self.residual_gate)
        gated_residual = residual_features * gate_value

        # 记录残差时间
        residual_time = time.time() - residual_start
        self.total_residual_time += residual_time

        # 更新计数
        self.forward_count += 1

        # 合并主输出和残差特征
        if self.training:
            # 训练阶段使用融合层
            combined = torch.cat([primary_output, gated_residual], dim=-1)
            final_output = self.fusion_layer(combined)
        else:
            # 推理阶段使用高效直接融合
            scaling_factor = 1.0 - gate_value * self.residual_ratio
            final_output = primary_output * scaling_factor

            # 直接添加残差信息
            pad_size = final_output.size(1) - gated_residual.size(1)
            if pad_size > 0:
                # 如果需要填充
                padded_residual = torch.cat([
                    gated_residual,
                    torch.zeros(gated_residual.size(0), pad_size, device=gated_residual.device)
                ], dim=1)
                final_output = final_output + padded_residual
            else:
                # 残差维度与输出匹配
                final_output = final_output + gated_residual

        return final_output

    def get_performance_stats(self):
        """获取性能统计信息"""
        forward_count = max(self.forward_count.item(), 1)
        avg_mapping_time = (self.total_mapping_time / forward_count).item() * 1000  # ms
        avg_residual_time = (self.total_residual_time / forward_count).item() * 1000  # ms

        flops = self.flops_estimate()

        gate_value = torch.sigmoid(self.residual_gate).item()

        return {
            "forward_count": forward_count,
            "avg_mapping_time_ms": avg_mapping_time,
            "avg_residual_time_ms": avg_residual_time,
            "total_time_ms": avg_mapping_time + avg_residual_time,
            "flops_mapping": flops['main_mapping'],
            "flops_residual": flops['residual'],
            "flops_fusion": flops['fusion'],
            "flops_total": flops['total'],
            "residual_gate_value": gate_value,
            "effective_residual_ratio": gate_value * self.residual_ratio
        }

    def optimize_for_inference(self):
        """优化模型用于推理"""
        with torch.no_grad():
            # 1. 固化特征权重
            final_weights = torch.sigmoid(self.feature_weights).detach()
            self.feature_weights.data = final_weights

            # 2. 固化频率重要性
            img_freq_importance = torch.sigmoid(self.img_frequency_importance).detach()
            geo_freq_importance = torch.sigmoid(self.geo_frequency_importance).detach()

            # 将频率重要性融合到权重中
            self.img_weights.data = self.img_weights * img_freq_importance.unsqueeze(0)
            self.geo_weights.data = self.geo_weights * geo_freq_importance.unsqueeze(0)

            # 3. 优化残差分支
            # 固化残差门控
            gate_value = torch.sigmoid(self.residual_gate).item()
            if gate_value < 0.1:
                # 如果门控值很小，完全禁用残差
                print("残差门控值很小 ({:.3f})，禁用残差连接以优化性能".format(gate_value))
                # 创建一个简单的直通层
                self.residual_compressor = nn.Identity()
                self.residual_gate.data = torch.tensor(0.0)
            else:
                print("残差门控值为 {:.3f}，保留残差连接".format(gate_value))
                # 固化门控值
                self.residual_gate.data = torch.tensor(gate_value)

            # 4. 移除训练时的融合层，使用更高效的直接融合
            if gate_value > 0.05:
                # 将融合层权重固化为恒等映射的近似
                self.fusion_layer = nn.Identity()

        # 重置性能计数器
        self.total_mapping_time.zero_()
        self.total_residual_time.zero_()
        self.forward_count.zero_()

        print("模型已优化用于推理，FLOPs:", self.flops_estimate()['total'] / 1e6, "M")


# 计算FLOPs的工具函数
def compute_hilbert_mapping_flops(input_dim, output_dim, batch_size=1, with_residual=False, residual_ratio=0.1):
    """计算希尔伯特映射的FLOPs

    Args:
        input_dim: 输入维度
        output_dim: 输出维度
        batch_size: 批量大小
        with_residual: 是否使用残差连接
        residual_ratio: 残差维度比例

    Returns:
        估计的FLOPs
    """
    # 标准希尔伯特映射FLOPs
    matrix_mul_flops = input_dim * (output_dim // 2)
    offsets_flops = output_dim // 2
    trig_flops = (output_dim // 2) * 2 * 5  # 假设每个三角函数操作为5个FLOPs

    standard_flops = matrix_mul_flops + offsets_flops + trig_flops

    if not with_residual:
        return standard_flops * batch_size

    # 残差部分FLOPs
    residual_dim = int(output_dim * residual_ratio)
    if residual_dim % 2 != 0:
        residual_dim += 1

    residual_projection_flops = input_dim * residual_dim
    residual_ln_flops = residual_dim * 3
    residual_activation_flops = residual_dim

    # 融合部分FLOPs (推理阶段的简化版本)
    fusion_flops = output_dim * 2

    total_flops = standard_flops + residual_projection_flops + residual_ln_flops + residual_activation_flops + fusion_flops
    return total_flops * batch_size
