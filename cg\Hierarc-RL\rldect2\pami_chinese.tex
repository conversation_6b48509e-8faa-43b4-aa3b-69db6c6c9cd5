\documentclass[journal]{IEEEtran}

% 中文支持包
\usepackage{fontspec}
\usepackage{xeCJK}

% 基础包
\usepackage{graphicx}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{cite}
\usepackage{url}
\usepackage{booktabs}  % 添加表格线包

% 设置中文字体 - 使用更通用的字体设置
\setCJKmainfont[BoldFont=SimHei]{SimSun}  % 宋体，粗体用黑体
\setCJKsansfont{SimHei}  % 黑体
\setCJKmonofont{FangSong} % 仿宋
% 如果上述字体不可用，可以尝试以下设置：
% \setCJKmainfont{Noto Serif CJK SC}
% \setCJKsansfont{Noto Sans CJK SC}

% 论文信息
\title{基于忆阻器增强网络和希尔伯特特征映射的分层强化学习目标检测方法}

\author{张三,~\IEEEmembership{学生会员,~IEEE,}
        李四,~\IEEEmembership{会员,~IEEE,}
        和~王五,~\IEEEmembership{高级会员,~IEEE}% <-this % stops a space
\thanks{张三就职于某某大学计算机科学与技术学院，
中国某市，邮编：100000，电子邮箱：<EMAIL>。}% <-this % stops a space
\thanks{李四和王五就职于某某研究院。}% <-this % stops a space
\thanks{稿件收到日期：\today。}}

% 页眉设置
\markboth{IEEE 期刊论文模板,~第~XX卷,~第~X期,~\today}%
{张三 \MakeLowercase{\textit{等}}: 基于忆阻器增强网络和希尔伯特特征映射的分层强化学习目标检测方法}

\begin{document}

\maketitle

\begin{abstract}
本文提出了一种新颖的分层强化学习框架用于目标检测，该框架集成了三个关键创新：(1) 残差增强希尔伯特映射(REHM)用于高效的特征降维，(2) 忆阻器启发的仿生深度Q网络(MRDQN)模拟突触可塑性，(3) \text{IoU}趋势振荡感知奖励机制(TORM)用于改善训练稳定性。我们的方法解决了深度强化学习中的维数灾难问题，同时保持了判别性特征表示。在标准目标检测数据集上的实验结果表明，与传统基于DQN的方法相比，在检测精度、训练效率和鲁棒性方面都有改善。
\end{abstract}

% IEEE格式关键词
\begin{IEEEkeywords}
强化学习，目标检测，忆阻器网络，希尔伯特映射，深度Q网络，特征降维。
\end{IEEEkeywords}

% IEEE同行评议标题页
\IEEEpeerreviewmaketitle

\section{引言}
% IEEE格式的首字母大写
目标检测被认为是计算机视觉最重要且最具挑战性的分支之一。目标检测旨在定位和识别图像中的语义目标实例。它已广泛应用于人们生活的许多领域，例如医学影像分析\cite{ref1}、遥感监测\cite{ref2}、工业质检\cite{ref3}、监控安防\cite{ref4}和自动驾驶\cite{ref5}等。在这些关键应用场景中，不仅需要准确的目标定位，更需要提供清晰的检测依据和推理过程，以满足专业决策和质量控制的严格要求。

近年来，许多基于深度学习的目标检测方法被提出\cite{ref6,ref7,ref8}。深度学习目标检测方法可以大致分为三类：两阶段检测器、单阶段检测器和基于Transformer的检测器。两阶段检测器的经典代表包括R-CNN\cite{ref9}、Fast R-CNN\cite{ref10}、Faster R-CNN\cite{ref11}、Mask R-CNN\cite{ref12}、Cascade R-CNN\cite{ref13}等。两阶段检测器通常采用区域提议网络提供大量区域提议，然后使用RoI池化\cite{ref11}或RoI对齐\cite{ref12}增强每个实例的局部特征进行分类和回归。R-CNN首次将CNN特征提取与目标检测相结合，虽然每张图像需要47秒的处理时间，但为后续研究奠定了基础。Fast R-CNN通过引入RoI池化层实现端到端训练，将处理速度提升了200倍。Faster R-CNN提出区域提议网络，在PASCAL VOC 2007数据集上达到73.2\%的mAP。Mask R-CNN通过RoI Align解决量化误差问题，Cascade R-CNN采用级联结构实现渐进式精细化。这类方法需要预计算大量冗余的区域提议。为了进一步提高检测效率，单阶段检测器被提出。单阶段检测器的经典代表包括YOLO\cite{ref14}、YOLOv2\cite{ref15}、YOLOv3\cite{ref16}、YOLOv4\cite{ref17}、YOLOv5\cite{ref18}、SSD\cite{ref19}、RetinaNet\cite{ref20}等。YOLO将目标检测重新定义为回归问题，实现45 FPS的实时检测但mAP仅为63.4\%。YOLOv2引入锚框机制，YOLOv3采用多尺度预测，YOLOv4和YOLOv5通过数据增强和损失函数优化进一步平衡精度与速度。SSD通过在不同尺度特征图上预测处理多尺度目标，在VOC 2007数据集上达到74.3\%的mAP和59 FPS。RetinaNet提出Focal Loss解决类别不平衡问题。这些方法直接从全局特征图预测目标的类别和位置，无需从原始图像中裁剪每个实例的区域，因此推理通常更快。近年来，基于Transformer的目标检测方法在计算机视觉领域取得了显著进展。DETR\cite{ref21}是这一方向的开创性工作，首次将Transformer架构引入目标检测任务。DETR通过Transformer编码器-解码器结构直接预测目标的类别和边界框，消除了传统方法中的区域提议和非极大值抑制步骤，但存在收敛速度慢和小目标检测性能不佳的问题。随后，Deformable DETR\cite{ref22}引入可变形注意力机制，显著降低了计算复杂度并提高了高分辨率图像处理的效率。此外，Swin Transformer\cite{ref23}利用分层视觉Transformer架构生成多尺度特征表示，通过移位窗口机制更高效地捕获局部和全局信息。DINO\cite{ref24}、DN-DETR\cite{ref25}等工作通过对比学习、去噪训练等技术进一步提升了性能。这些创新的基于Transformer的方法不仅提高了目标检测的精度和效率，还为未来研究指出了新方向。

然而，大多数基于深度学习的方法严重依赖卷积神经网络来密集预测每个实例的类别和精确位置。这涉及使用大量预定义锚点，导致相当大的计算开销\cite{ref11}。两阶段方法需要生成1000-2000个候选区域并对每个区域进行独立处理，单阶段方法需要在特征图的每个位置预测多个锚框，产生大量负样本。基于Transformer的目标检测方法最初在处理高分辨率图像时面临高计算和内存需求，因为自注意力机制需要计算所有像素之间的注意力权重。虽然视觉Transformer\cite{ref26}通过将图像分割为固定大小补丁来缓解这个问题，显著降低了相比像素级注意力的计算开销，但计算需求对于资源受限设备仍然具有挑战性。更重要的是，这些方法都遵循固定的处理范式，缺乏类似人类视觉系统的选择性注意机制，无法根据图像内容自适应地分配计算资源，在复杂背景或目标稀疏的场景中难以提供可解释的检测过程。
这些方法基本遵循“全图卷积+全图回归”的经典范式，导致在图像背景复杂或目标稀疏的场景中存在显著的计算冗余，难以满足边缘部署对低延迟与低功耗的实际需求。

与前面提到的方法不同，基于深度强化学习的目标检测方法通过训练配备顺序交互能力的智能体来探索目标定位\cite{ref27,ref28,ref29}。深度强化学习目标检测方法通过在图像中连续调整检测框的位置和大小来工作，智能体从环境中获得反馈并更新其策略。具体而言，智能体在每个状态（图像或图像区域）中执行动作（移动或调整检测框），根据检测结果的精度接收奖励（正向或负向），然后优化其策略以在未来检测中最大化累积奖励。通过反复试验，智能体最终学会如何准确检测和定位图像中的目标。这种从全局到局部的聚焦目标检测方法使智能体能够用更少的步骤定位目标，模拟了人类视觉的选择性注意机制，不仅减少了区域提议的数量，还提供了清晰可解释的搜索路径，在医学诊断、安全检查等关键应用中具有重要价值。此外，这类方法能够根据图像内容的复杂程度自适应调整搜索策略，在少样本学习场景下展现出独特优势。

在这方面已经进行了许多研究。Caicedo等人\cite{ref30}引入了基于强化学习的主动目标检测方法，采用深度Q网络\cite{ref31}开发目标定位的动作策略，定义了9个基本动作（8个方向移动和1个终止动作）。这种方法持续搜索目标直到启动终止动作，使模型能够有效定位单个目标实例，在不依赖目标提议的情况下实现高性能，但存在收敛速度慢的问题。此外，Bueno等人\cite{ref32}引入了基于强化学习原理的分层搜索策略，包含五种不同的搜索动作。训练的智能体选择性地关注包含大量目标相关信息的区域，随后细化定位区域以进行进一步审查，通过粗到细的搜索过程提升了检测效率。Wang等人\cite{ref33}提出了使用多任务学习方法的DQN来定位特定类别的目标。该框架包含动作执行智能体和终止智能体，通过联合优化在最小化步骤的同时实现了更高的平均精度。此外，Jie等人\cite{ref34}将目标间的全局相互依赖关系整合到目标定位过程中，提出了新颖的树结构强化学习方法。这种方法巧妙地导航定位区域，利用当前观察和历史搜索路径顺序搜索目标。令人印象深刻的是，这种方法在显著减少所需区域提议数量的同时，实现了与流行区域提议算法相当的召回率。Pirinen等人\cite{ref35}提出了基于深度强化学习的视觉识别模型drl-RPN，用深度强化学习训练的顺序注意力机制替代贪婪的RoI选择过程，实现了更智能的区域选择策略。Zhou等人\cite{ref36}引入了系统化的LHAR-RLD架构，包含低维RepVGG、混合DQN、自适应动态奖励函数和ROI对齐边界框回归网络四个核心组件，在VOC2007数据集上达到74.4\%的mAP，计算复杂度仅为1.43GFLOPs，相比传统强化学习方法实现了10倍的计算效率提升。Chen等人\cite{ref37}针对特征提取不足问题，使用了包含通道注意力机制的VGG16特征提取模块作为强化学习的状态输入，真实框和预测框的中心点距离以及长宽比被额外考虑以改进奖励机制，该方法只需要4-10个候选框来检测目标。

然而，这些方法存在潜在的缺陷。这些包括特征提取器的有限能力、Nature DQN内的受限学习能力、严格预定义的奖励函数，以及强化学习固有的受限离散动作空间。深入分析现有强化学习检测方法，我们发现其面临的核心技术挑战主要体现在以下几个方面。

首先是状态表示的维数灾难问题。以Zhou等人的LDR为例，虽然采用RepVGG-A0和自适应平均池化进行降维，但12288维视觉特征与8维几何特征、90维动作历史结合后形成的12386维状态表示仍然过高，缺乏理论保证的特征保持机制。这种高维状态空间不仅增加了Q网络的学习难度，简单的线性降维更可能导致关键判别信息的丢失。

策略学习的稳定性问题同样突出。传统DQN及其改进版本面临过估计、灾难性遗忘和训练不稳定等固有问题。Zhou等人的HDQN虽然结合了Double DQN、优先经验回放和Dueling DQN，但这种简单的技术组合并未从根本上解决问题。更重要的是，现有方法缺乏生物启发的记忆机制，评估网络和目标网络采用相同的参数更新策略也限制了学习的灵活性。

奖励信号的设计缺陷进一步制约了方法的性能。现有奖励函数设计过于简化，如Zhou等人的ADR基本形式r + ΔIoU + ζt无法捕捉复杂的行为模式，容易导致"奖励震荡"现象。当IoU在某个数值附近小幅波动时，奖励信号频繁在正负之间切换，产生高频噪声，使智能体在局部饱和区域出现抖动和漂移行为。

此外，精确定位能力的限制也是一个关键问题。现有回归方法如Zhou等人的RABRNet虽然使用ROI对齐保持空间信息，但仍受限于传统IoU损失的梯度消失问题。当预测框与真实框不重叠时，IoU损失无法提供有效的优化方向。传统的边界框参数化方式主要关注中心点和尺寸，忽略了角点在几何结构中的重要作用，在处理小目标和不规则目标时精度有限。因此，基于强化学习的目标检测算法仍然具有性能增强的未开发潜力。

为了解决图像状态表示不足和强化学习智能体学习能力波动等问题，并在保持强化学习方法独特优势的同时提高目标检测的精度和计算效率，本文提出了MHT-RL。我们的主要贡献体现在以下几个方面：首先，我们提出了REHM，通过随机傅里叶特征的希尔伯特空间映射和余弦正弦双路变换将高维特征投影到低维正交空间，结合门控残差连接保持关键判别信息，实现从12386维到4096维的有效降维，压缩率达67\%，相比传统线性降维方法具有更强的理论保证。其次，我们提出了MRDQN来增强智能体的学习能力。该算法采用忆阻器启发的记忆强度调制机制和非对称双网络设计，通过TD误差驱动的重要性更新模拟突触可塑性，评估网络使用较高的衰减率实现快速适应，目标网络使用较低的衰减率维持稳定记忆，有效减少灾难性遗忘。第三，我们提出了针对强化学习目标检测任务的TORM。该机制基于5步滑动窗口分析IoU变化趋势，通过结构化趋势编码识别连续改进、连续退化和振荡三种行为模式，结合IoU区间自适应系数和训练进度权重提供精细化奖励引导，在低IoU区间对改进行为给予1.5倍奖励，在高IoU区间对退化行为施加1.5倍惩罚，有效抑制振荡行为并提升策略稳定性。最后，我们提出了DICR，创新性地将角点损失引入强化学习检测的回归后处理，结合DIoU损失的全局几何约束和角点加权损失的局部精细化，通过组合损失函数优化边界框定位精度，实现亚像素级定位。实验结果表明，我们的方法在标准数据集上相比现有最佳强化学习检测方法实现了显著性能提升，平均IoU提升0.073，振荡行为减少41.3\%，局部最优减少26.8\%，同时保持了合理的计算复杂度。


\section{方法}

\subsection{问题建模与整体架构}

\subsubsection{强化学习目标检测的MDP建模}

我们将目标检测建模为马尔可夫决策过程\cite{ref23}，智能体通过离散动作序列逐步优化边界框坐标。给定输入图像$I$和初始边界框$b_0$，智能体执行动作序列$\{a_1,a_2,\ldots,a_T\}$定位目标物体。每个动作$a_t\in\{0,1,\ldots,8\}$表示八个方向移动（上、下、左、右及四个对角方向）和一个终止动作。

状态表示$S_t$结合视觉特征、几何属性和动作历史，具体构成为：
\begin{equation}
s_t = [\mathbf{f}_{\text{visual}}(I, b_t); \mathbf{f}_{\text{geo}}(b_t); \mathbf{h}_{\text{action}}(a_{t-k:t-1})]
\label{eq:state_representation}
\end{equation}

其中$\mathbf{f}{visual}(I, b_t) \in \mathbb{R}^{d_v}$表示从当前边界框区域提取的视觉特征，$\mathbf{f}{geo}(b_t) \in \mathbb{R}^8$包含归一化的边界框坐标、宽高比、中心位置等几何信息，$\mathbf{h}{action}(a{t-k:t-1}) \in \mathbb{R}^{k \times 9}$编码最近10步的动作历史。这种多模态状态表示能够充分刻画当前检测状态，为策略学习提供丰富的上下文信息。

奖励函数$r_t$不仅评估即时\text{IoU}改进，还考虑长期行为模式：
\begin{align}
r_t &= r_{\text{IoU}}(b_t, b_{t+1}, b_{gt}) \nonumber \\
    &\quad + r_{trend}(\{\text{IoU}_{t-k:t}\}) \nonumber \\
    &\quad + r_{efficiency}(t)
\label{eq:reward_function}
\end{align}

智能体的目标是学习最优策略$\pi^*(s_t) = \arg\max_a Q^*(s_t, a)$，在保证检测精度的同时最小化计算成本和搜索步数。

\subsubsection{MHT-RL系统架构与数据流}

MHT-RL框架采用数据驱动的分层处理架构，通过五个核心组件的协同工作实现高效的强化学习目标检测。整体系统的数据流动路径如下：
输入图像$I \in \mathbb{R}^{H \times W \times 3}$和初始边界框$b_0$首先经过RepVGG-A0\cite{ref24}骨干网络进行特征提取。RepVGG-A0产生192×8×8的特征图，展平后得到12288维的视觉特征向量$\mathbf{f}{\text{visual}} \in \mathbb{R}^{12288}$。随着系统构建多模态状态表示，将视觉特征与8维几何特征$\mathbf{f}{\text{geo}}$（包含归一化边界框坐标、宽高比、中心位置等）和90维动作历史编码$\mathbf{h}_{\text{action}}$（记录最近10步的one-hot动作向量）进行拼接，形成12386维的高维状态表示$s_t$。

高维状态$s_t$输入REHM模块进行降维处理。REHM采用分组映射策略，对视觉特征和几何特征分别应用随机傅里叶变换\cite{ref25}，通过余弦正弦双路映射将原始12386维状态压缩至4096维紧凑表示$\mathbf{z}_{\text{final}}$。降维过程中，门控残差机制$\mathbf{z} = \phi(\mathbf{x}) + \alpha \cdot \mathbf{g} \odot \mathbf{C}(\mathbf{x})$确保关键判别信息的保留。自适应频率重要性学习机制动态调整不同频率分量的权重，在实现约67\%维度压缩的同时保持特征判别性。

降维后的4096维状态表示输入MRDQN进行动作价值估计。MRDQN包含评估网络和目标网络两个分支，每个分支均采用忆阻器层替代传统全连接层。忆阻器层通过有效权重机制$\mathbf{W}_{\text{eff}} = \mathbf{W} \odot \sigma(\mathbf{M} \cdot \mathbf{I})$动态调制连接强度，其中记忆强度矩阵$\mathbf{M}$和重要性权重矩阵$\mathbf{I}$分别记录历史激活模式和任务相关性。网络输出9维动作价值向量，对应8个方向移动和1个终止动作，通过$\epsilon$-贪婪策略选择最优动作$a_t$。

每次动作执行后，TORM模块分析\text{IoU}变化序列$\{\text{IoU}_{t-4}, ..., \text{IoU}_t\}$，识别连续改进、连续退化和振荡三种行为模式。基于5步滑动窗口的趋势检测算法计算趋势强度和持续步数，结合\text{IoU}区间自适应系数（低\text{IoU}区间改进奖励1.5倍，高\text{IoU}区间退化惩罚1.5倍）和训练进度权重$(0.3 + \text{progress} \times 0.7)$，生成精细化奖励信号$r_t = r_{\text{base}} + r_{\text{trend}} \times w_{\text{trend}}$指导策略优化。

当智能体选择终止动作时，系统激活DICR模块进行精确定位。DICR直接使用RepVGG-A0提取的完整分辨率特征（未经希尔伯特降维），通过两个1×1卷积层（192→256→64通道）和自适应平均池化构建回归头，输出4维边界框偏移量$(\Delta x_1, \Delta x_2, \Delta y_1, \Delta y_2)$。损失函数结合DIoU损失\cite{ref26}和角点加权损失$\mathcal{L}_{\text{DICR}} = \lambda_{\text{diou}} \mathcal{L}_{\text{DIoU}} + \lambda_{\text{corner}} \mathcal{L}_{\text{corner}}$，实现亚像素级定位精度。

\subsubsection{两阶段解耦训练策略}

强化学习的高方差特性和回归网络对稳定特征表示的需求存在冲突，因此我们采用分阶段解耦训练策略避免不同优化目标间的相互干扰。第一阶段专注于强化学习策略优化，冻结DICR模块参数，训练MRDQN在REHM降维特征空间中学习有效的探索策略，同时利用TORM提供的趋势感知奖励避免局部最优和振荡行为。第二阶段冻结所有强化学习组件（REHM、MRDQN、TORM），基于第一阶段获得的粗定位结果训练DICR回归网络，利用完整分辨率特征实现亚像素级定位精度。这种"先粗后精"的分治策略有效避免了强化学习的不稳定性对精确回归的干扰，在保证探索效率的同时确保了最终检测精度。


\subsection{残差增强希尔伯特映射(REHM)}

\subsubsection{高维状态表示的RL挑战与解决思路}

强化学习目标检测中，智能体状态$s_t$需同时编码视觉特征$\mathbf{f}_{\text{visual}} \in \mathbb{R}^{12288}$、几何属性$\mathbf{f}_{\text{geo}} \in \mathbb{R}^8$和动作历史$\mathbf{h}_{\text{action}} \in \mathbb{R}^{90}$，总维度达12386维。如此高维状态空间带来严重挑战：智能体需要指数级样本量才能有效覆盖状态空间，而强化学习本身就面临样本稀缺问题。高维空间中的价值函数极易过拟合，导致Bellman误差累积和训练不稳定。此外，随机探索策略在高维空间中几乎无法发现有价值的状态转移。

现有降维方法在强化学习场景下存在根本性限制。PCA等线性方法无法捕捉状态-动作价值函数的复杂非线性结构，且丢失的局部判别信息恰恰是动作选择的关键依据\cite{ref28}。自编码器类方法虽能处理非线性，但需要大量无标签数据进行预训练，与强化学习的在线学习范式不符\cite{ref31}。t-SNE等流形学习方法\cite{ref29}的非参数化特性无法处理训练过程中不断出现的新状态，不支持强化学习所需的增量学习\cite{ref30}。

为解决上述问题，我们提出残差增强希尔伯特映射(REHM)。该方法基于随机傅里叶特征理论，通过将高维状态映射到低维希尔伯特再生核空间，在保持判别性的同时显著降低计算复杂度。与传统随机映射主要用于分类任务不同，REHM专门针对强化学习的非平稳性和时序依赖特点进行设计，通过残差增强和频率调控机制扩展了随机傅里叶特征在强化学习任务中的适用性。

\subsubsection{随机傅里叶特征理论基础与多模态分组映射}

REHM的核心基于Rahimi-Recht随机傅里叶特征理论\cite{ref25}。对于RBF核$k(\mathbf{x}, \mathbf{y}) = \exp(-\gamma||\mathbf{x} - \mathbf{y}||^2)$，通过Bochner定理\cite{ref27}可构造随机映射$\phi(\mathbf{x}) = \sqrt{\frac{2}{D}}[\cos(\mathbf{W}^T\mathbf{x} + \mathbf{b}), \sin(\mathbf{W}^T\mathbf{x} + \mathbf{b})]^T$，该映射满足$\mathbb{E}[\phi(\mathbf{x})^T\phi(\mathbf{y})] = k(\mathbf{x}, \mathbf{y})$的无偏估计性质。

考虑到强化学习状态的多模态特性，我们设计分组映射策略以适应不同特征类型的统计差异。给定输入状态$\mathbf{s} = [\mathbf{f}_{\text{visual}}; \mathbf{f}_{\text{geo}}; \mathbf{h}_{\text{action}}] \in \mathbb{R}^{12386}$，我们将其分解为视觉特征组$\mathbf{x}_{\text{img}} \in \mathbb{R}^{12288}$和语义特征组$\mathbf{x}_{\text{sem}} \in \mathbb{R}^{98}$（包含几何和动作信息）。这种分组处理的动机在于不同模态特征具有显著不同的统计特性和对检测任务的贡献模式。分组映射的拼接结果$\phi(\mathbf{x}) = [\phi_{\text{img}}(\mathbf{x}_{\text{img}}); \phi_{\text{sem}}(\mathbf{x}_{\text{sem}})]$对应于加性核$k(\mathbf{x}, \mathbf{y}) = k_{\text{img}}(\mathbf{x}_{\text{img}}, \mathbf{y}_{\text{img}}) + k_{\text{sem}}(\mathbf{x}_{\text{sem}}, \mathbf{y}_{\text{sem}})$，从而保持了RKHS的核近似性质。

针对不同模态特征的统计差异，我们采用差异化的带宽参数设计分组映射：
\begin{align}
\phi_{\text{img}}(\mathbf{x}_{\text{img}}) &= \sqrt{\frac{2}{D_{\text{img}}}} \begin{bmatrix} \cos(\mathbf{W}_{\text{img}}\mathbf{x}_{\text{img}} + \mathbf{b}_{\text{img}}) \\ \sin(\mathbf{W}_{\text{img}}\mathbf{x}_{\text{img}} + \mathbf{b}_{\text{img}}) \end{bmatrix} \label{eq:hilbert_img} \\
\phi_{\text{sem}}(\mathbf{x}_{\text{sem}}) &= \sqrt{\frac{2}{D_{\text{sem}}}} \begin{bmatrix} \cos(\mathbf{W}_{\text{sem}}\mathbf{x}_{\text{sem}} + \mathbf{b}_{\text{sem}}) \\ \sin(\mathbf{W}_{\text{sem}}\mathbf{x}_{\text{sem}} + \mathbf{b}_{\text{sem}}) \end{bmatrix} \label{eq:hilbert_sem}
\end{align}

维度分配的具体推导如下：设目标输出维度为$D_{\text{total}} = 4096$，根据特征重要性分配原则，视觉特征分配$D_{\text{img}} = 3686$维（占90\%），语义特征分配$D_{\text{sem}} = 410$维（占10\%）。由于余弦正弦双路映射，实际的投影矩阵维度为$\mathbf{W}_{\text{img}} \in \mathbb{R}^{12288 \times 1843}$和$\mathbf{W}_{\text{sem}} \in \mathbb{R}^{98 \times 205}$，分别从$\mathcal{N}(0, \gamma^2\mathbf{I})$采样，其中$\gamma = 0.01$。

最终的希尔伯特映射输出为：
\begin{equation}
\phi(\mathbf{x}) = [\phi_{\text{img}}(\mathbf{x}_{\text{img}}); \phi_{\text{sem}}(\mathbf{x}_{\text{sem}})] \in \mathbb{R}^{4096}
\label{eq:final_mapping}
\end{equation}

\subsubsection{残差增强与自适应频率学习机制}

然而，纯粹的随机傅里叶映射可能丢失对Q函数逼近至关重要的低频全局信息，特别是状态间的长程依赖关系。这种信息丢失在强化学习中尤为致命，因为价值函数的准确估计依赖于对状态转移模式的全面理解。为解决这一问题，我们设计了门控残差增强机制：
\begin{equation}
\mathbf{z} = \phi(\mathbf{x}) + \alpha \cdot \mathbf{g} \odot \mathbf{C}(\mathbf{x})
\label{eq:residual_enhancement}
\end{equation}

其中$\mathbf{C}(\mathbf{x}) = \mathbf{W}_c \mathbf{x} + \mathbf{b}_c \in \mathbb{R}^{D_r}$是原始特征的线性压缩（$D_r = 0.5 \times D = 2048$），$\mathbf{g} = \sigma(\mathbf{W}_g \mathbf{x} + \mathbf{b}_g) \in \mathbb{R}^{D_r}$是可学习的门控向量，$\alpha = 0.5$是全局残差系数。

该机制通过保持原始特征空间的线性结构来增强Q函数的连续性，同时保持状态序列的时序相关性，从而提升探索效率。

进一步地，不同强化学习任务对频率分量的需求存在显著差异，固定的频率权重无法适应训练过程中动态变化的学习需求。在探索阶段，智能体需要更多高频细节信息来发现新的有效策略；而在利用阶段，低频全局信息对于稳定价值估计更为重要。基于这一观察，我们为图像和语义特征分别引入可学习的频率重要性参数：
\begin{align}
\mathbf{w}_{\text{img}} &= \sigma(\mathbf{p}_{\text{img}}) \label{eq:img_freq_weights} \\
\mathbf{w}_{\text{sem}} &= \sigma(\mathbf{p}_{\text{sem}}) \label{eq:sem_freq_weights}
\end{align}

其中$\mathbf{p}_{\text{img}}$和$\mathbf{p}_{\text{sem}}$是可学习参数。频率加权后的投影为：
\begin{equation}
\phi_{\text{weighted}} = [\mathbf{w}_{\text{img}} \odot \phi_{\text{img}}(\mathbf{x}_{\text{img}}); \mathbf{w}_{\text{sem}} \odot \phi_{\text{sem}}(\mathbf{x}_{\text{sem}})]
\label{eq:adaptive_mapping}
\end{equation}

通过这种自适应频率调整机制，REHM能够根据当前学习阶段的需求动态平衡不同频率分量的重要性，直接服务于强化学习中的探索-利用权衡。需要注意的是，虽然残差增强和频率加权机制打破了传统随机傅里叶特征的严格核近似性质，但它们作为任务导向的特征增强器，在强化学习的非平稳环境中能够显著提升策略学习的稳定性和收敛速度。

\subsection{忆阻器启发的仿生深度Q网络(MRDQN)}

大多数基于强化学习的目标检测算法使用标准DQN来学习策略，但标准DQN在处理复杂任务时容易出现Q值过估计、样本效率低和策略更新不稳定等问题。虽然现有的改进方法如Double DQN、优先经验回放DQN和Dueling DQN在某些方面分别取得了显著成果，但它们各自的局限性仍然存在。更重要的是，这些方法都缺乏生物启发的记忆机制，无法有效处理强化学习中的灾难性遗忘问题。因此，我们提出忆阻器启发的仿生深度Q网络(MRDQN)，这是一种将生物神经可塑性机制引入深度强化学习框架的创新方法，旨在克服传统DQN算法的问题并提高强化学习性能。

首先，MRDQN融合了Double DQN的思想，在计算Q值阶段，将动作评估从评估网络改为目标网络，而动作选择仍由评估网络操作，这可以有效缓解标准DQN算法中容易出现的过估计问题，提高学习的稳定性和准确性。详细方程如下：
\begin{align}
Q_{\text{eval}} &= Q(s, a;\theta^e) \label{eq:q_eval} \\
Q_{\text{tar}}^{\text{old}} &= r + \gamma \max_{a'} Q(s', a';\theta^t) \label{eq:q_tar_old} \\
Q_{\text{tar}} &= r + \gamma Q(s', \arg\max_{a'} Q(s', a';\theta^e);\theta^t) \label{eq:q_tar_new}
\end{align}
其中$\theta^e$表示评估网络的权重，$\theta^t$表示目标网络的权重，$\gamma$表示折扣因子，影响智能体对未来奖励的重视程度。

其次，在从记忆缓冲区抽取样本的阶段，MRDQN引入了优先经验回放的概念，使用TD误差作为优先级指标来计算样本的优先级信息，使训练优先关注那些对更新网络参数重要的样本。这种优先采样策略使智能体更多地关注提供更多学习信息的样本，提高采样效率，使算法收敛更快，同时增加训练的稳定性。在我们的任务中，TD误差计算如下：
\begin{equation}
\text{TD} = Q_{\text{tar}} - Q_{\text{eval}} = r + \gamma Q(s', \arg\max_{a'} Q(s', a';\theta^e);\theta^t) - Q(s, a;\theta^e)
\label{eq:td_error}
\end{equation}

此外，在网络架构方面，MRDQN采用了Dueling DQN的网络架构，将网络结构在网络输出之前分为状态分支和价值分支。这种架构的使用使算法能够更好地处理高度相关的动作，提高学习的效率和稳定性。为了解决方程(12)中Q值和V值建模的非唯一性问题并提高输出的稳定性，方程(12)在语义上修改为方程(13)：
\begin{align}
Q(s, a; \theta, \alpha, \beta) &= V(s; \theta, \alpha) + A(s, a; \theta, \beta) \label{eq:dueling_original} \\
Q(s, a; \theta, \alpha, \beta) &= V(s; \theta, \alpha) + A(s, a; \theta, \beta) - \frac{1}{|A|} \sum_{a'} A(s, a'; \theta, \beta) \label{eq:dueling_modified}
\end{align}

然而，传统的DQN改进方法仍然缺乏生物启发的记忆机制。受生物神经系统中突触可塑性的启发，我们在MRDQN中引入了忆阻器层来替代传统的全连接层。每个忆阻器层除了标准的权重矩阵$\mathbf{W} \in \mathbb{R}^{m \times n}$和偏置向量$\mathbf{b} \in \mathbb{R}^m$外，还维护两个关键的状态矩阵：记忆强度矩阵$\mathbf{M} \in \mathbb{R}^{m \times n}$记录每个连接的历史激活强度，重要性权重矩阵$\mathbf{I} \in \mathbb{R}^{m \times n}$基于任务相关性评估连接重要性。有效权重矩阵计算为：
\begin{equation}
\mathbf{W}_{\text{eff}} = \mathbf{W} \odot \sigma(\mathbf{M} \cdot \mathbf{I})
\label{eq:effective_weights}
\end{equation}
该设计通过sigmoid函数将历史依赖性的乘积映射至$(0,1)$区间，实现软门控机制。记忆强度的更新基于输入激活模式和权重使用频率：
\begin{equation}
\mathbf{M}_{t+1} = (1-\lambda_d)\mathbf{M}_t + \lambda_a \cdot \mathbf{A}_t \odot |\mathbf{W}_t|
\label{eq:memory_update}
\end{equation}
其中$\lambda_d$是记忆衰减率，$\lambda_a$是激活强度系数，$\mathbf{A}_t$是输入激活强度矩阵。重要性权重基于TD误差进行更新：
\begin{equation}
\mathbf{I}_{t+1} = \mathbf{I}_t + \beta \cdot |\delta_t|
\label{eq:importance_update}
\end{equation}
其中$\beta$是重要性更新率，$\delta_t$是TD误差。

MRDQN的一个关键创新是采用非对称双网络架构，我们对评估网络和目标网络采用差异化的忆阻器参数配置，形成"快速学习-稳定记忆"的双网络架构。这种设计的理论依据在于强化学习中探索与利用的权衡：评估网络需要快速探索新策略，因此采用较高的可塑性参数；目标网络负责提供稳定的价值基准，故采用较低的可塑性参数。具体的参数配置如表\ref{tab:mrdqn_params}所示。

\begin{table}[htbp]
\centering
\caption{MRDQN非对称双网络参数配置}
\label{tab:mrdqn_params}
\begin{tabular}{lcc}
\toprule
参数 & 评估网络 & 目标网络 \\
\midrule
记忆衰减率 $\lambda_d$ & 0.03-0.04 & 0.002-0.003 \\
重要性更新率 $\beta$ & 0.015-0.02 & 0.0008-0.001 \\
激活强度 $\lambda_a$ & 0.12-0.15 & 0.04-0.05 \\
网络更新频率 & 每步 & 每50步 \\
\bottomrule
\end{tabular}
\end{table}

评估网络使用较高的衰减率便于快速适应新经验，目标网络使用较低的衰减率维持稳定的价值基准。目标网络的$\lambda_d$约为评估网络的1/10，这种设置有助于减缓参数更新速度，提供相对稳定的学习目标。两网络间的参数同步不再是简单的复制，而是基于重要性和稳定性的选择性同步，重要连接采用较大的同步权重以快速同步，非重要连接采用较小的同步权重以保持稳定。

为进一步增强学习稳定性，MRDQN引入了元可塑性机制来跟踪参数变化历史并识别不稳定区域。我们对每个连接维护其参数变化历史$\mathbf{H}_{t+1} = \rho \mathbf{H}_t + (1-\rho) |\mathbf{W}_{t+1} - \mathbf{W}_t|$，其中$\rho = 0.9$为动量系数。当检测到变化剧烈的区域时，对相应连接增强记忆强度$\mathbf{M}_{i,j} \leftarrow \mathbf{M}_{i,j} + \kappa \cdot \mathbf{H}_t(i,j)$，其中$\kappa = 0.05$是稳定性增强系数。这种机制确保了对变化剧烈区域的记忆保持能力，防止重要连接在快速学习过程中被错误遗忘。

此外，MRDQN还引入了选择性同步机制来替代传统DQN的简单参数复制策略。选择性同步基于连接的重要性和记忆强度为每个连接计算自适应同步权重$\tau_{ij}$，重要性高且记忆强度大的连接获得较大的同步权重实现快速同步，而非重要连接则保持稳定。同时，系统还根据近期奖励动态调整同步频率：当平均奖励较高时增加同步频率以巩固良好策略，当平均奖励较低时降低同步频率以避免传播错误信息。这种设计有效缓解了传统DQN的过估计问题和灾难性遗忘现象，显著提升了强化学习的稳定性和收敛速度。

值得强调的是，MRDQN所引入的状态调控机制本质上独立于具体网络结构，未来可扩展至Transformer或图神经网络中以增强长期记忆建模能力。

\subsection{IoU趋势振荡感知奖励机制(TORM)}

\subsubsection{振荡行为问题分析与趋势感知动机}

传统强化学习目标检测中，智能体经常出现振荡行为：在某些区域反复执行改进和退化动作，导致训练效率低下和局部最优\cite{ref22}。现有基于IoU的奖励机制$r_t = \text{sign}(\text{IoU}_t - \text{IoU}_{t-1})$存在固有的"奖励震荡"问题：当IoU在某个数值附近小幅波动时（如$0.65 \leftrightarrow 0.63 \leftrightarrow 0.66$），奖励信号会频繁在+1和-1之间切换，产生高频噪声。这种奖励震荡使得智能体无法获得稳定的策略梯度，导致在IoU局部饱和区域出现"局部抖动"和"目标漂移"现象。TORM的设计目标正是通过建模行为趋势持续性来抑制奖励震荡，提升策略学习的稳定性。

\subsubsection{行为模式识别与结构化趋势编码}

为解决这一问题，TORM维护长度为5的IoU滑动窗口$W_t = \{\text{IoU}_{t-4}, \text{IoU}_{t-3}, ..., \text{IoU}_t\}$和对应的差值序列$\{\Delta \text{IoU}_{t-3}, ..., \Delta \text{IoU}_t\}$，其中$\Delta \text{IoU}_i = \text{IoU}_i - \text{IoU}_{i-1}$表示第$i$步的IoU变化量。

为了将时序趋势以结构化形式编码为奖励因子，我们将其分解为三个相互独立但可乘的维度：方向性（改进/退化/振荡）、强度（单位IoU变化量）和持续性（步数）。这种分解方式不仅易于训练阶段的插值调节，也具备行为可解释性。

通过分析最近3步的差值符号模式，系统能够识别三种典型的智能体行为类型：连续改进模式（$\Delta \text{IoU}_{t-2} > 0, \Delta \text{IoU}_{t-1} > 0, \Delta \text{IoU}_t > 0$）表明智能体正在持续优化边界框定位；连续退化模式（$\Delta \text{IoU}_{t-2} < 0, \Delta \text{IoU}_{t-1} < 0, \Delta \text{IoU}_t < 0$）表明智能体陷入持续恶化状态；振荡模式则通过差值序列的符号变化识别，表明智能体在局部区域反复震荡。例如，若智能体在某一帧持续出现IoU上下波动（如$0.51 \to 0.49 \to 0.52 \to 0.48$），TORM将其识别为典型振荡模式，立即施加$r_{\text{trend}} < 0$的负反馈，从而引导其跳出该局部区域，探索潜在更优区域。

\subsubsection{自适应趋势奖励计算与IoU区间调节}

基于识别的行为模式，TORM构建趋势感知奖励机制。最终奖励由基础奖励和加权趋势奖励组成：
\begin{equation}
r_t = r_{\text{base}} + r_{\text{trend}}
\label{eq:torm_reward}
\end{equation}

其中$r_{\text{base}} = \text{sign}(\text{IoU}_t - \text{IoU}_{t-1})$是传统的单步IoU奖励。趋势奖励$r_{\text{trend}}$在计算过程中已经包含了训练进度权重$w_{\text{progress}} = 0.3 + \text{training\_progress} \cdot 0.7$的调制，其中$\text{training\_progress} \in [0,1]$表示当前训练进度的归一化值，确保趋势奖励的影响随训练进展从30\%逐步增强至100\%。趋势奖励的具体计算综合考虑IoU区间自适应系数和连续步数因子：

对于连续改进模式：
\begin{align}
r_{\text{trend}} &= c_{\text{improve}} \cdot s_{\text{trend}} \cdot f_{\text{steps}} \label{eq:improvement_reward} \\
\text{其中} \quad f_{\text{steps}} &= \min(2.0, 1.0 + (n_{\text{steps}} - 3) \cdot 0.25) \label{eq:steps_factor}
\end{align}

对于连续退化模式：
\begin{equation}
r_{\text{trend}} = -c_{\text{decline}} \cdot s_{\text{trend}} \cdot f_{\text{steps}}
\label{eq:decline_penalty}
\end{equation}

对于振荡模式：
\begin{equation}
r_{\text{trend}} = -c_{\text{oscillation}} \cdot 0.5
\label{eq:oscillation_penalty}
\end{equation}

其中$s_{\text{trend}} = \frac{1}{3}\sum_{i=0}^{2}|\Delta \text{IoU}_{t-i}|$是趋势强度，表示最近3步\text{IoU}变化幅度的平均值，$n_{\text{steps}}$是连续行为的持续步数。自适应系数$c_{\text{improve}}, c_{\text{decline}}, c_{\text{oscillation}}$根据当前\text{IoU}区间动态调整：低\text{IoU}区间（< 0.4）强化改进行为（$c_{\text{improve}} = 1.5, c_{\text{decline}} = 0.8, c_{\text{oscillation}} = 0.5$），高\text{IoU}区间（> 0.7）则相反（$c_{\text{improve}} = 0.8, c_{\text{decline}} = 1.5, c_{\text{oscillation}} = 1.0$），中等区间（0.4-0.7）采用平衡策略（$c_{\text{improve}} = 1.2, c_{\text{decline}} = 1.0, c_{\text{oscillation}} = 0.7$）。这种设计使得智能体在不同定位精度阶段获得针对性的行为引导。

相较于传统的单步\text{IoU}奖励机制，TORM通过建模行为趋势持续性，提升了奖励信号的鲁棒性与策略收敛性，有效抑制了\text{IoU}局部饱和区域的振荡行为，改善了训练稳定性和检测精度。

\subsection{角点融合距离IoU回归(DICR)}

强化学习阶段虽然能够实现有效的粗定位，但受限于离散动作空间，难以达到亚像素级的精确定位。传统IoU损失在边界框不重叠时梯度为零，无法提供有效的优化方向\cite{ref36}。现有回归方法主要关注边界框中心点或整体几何约束，忽略了角点作为边界框几何结构关键节点的重要性\cite{ref37}。

我们首次将角点损失用于强化学习目标检测回归后处理的模块。相比于传统的中心点回归，角点优化在两个方面表现突出：角点直接决定边界框的几何形状，对其精确定位能够同时约束宽度、高度和位置信息；在检测的微调阶段，角点损失能够提供更细粒度的局部梯度信息，特别适合修正强化学习粗定位后的边界偏差。DICR通过组合DIoU的全局几何约束和角点加权损失的局部精细化优化，实现了从粗到精的两阶段定位策略。

DIoU损失通过引入中心点距离惩罚项解决梯度消失问题：
\begin{equation}
\mathcal{L}_{\text{DIoU}} = 1 - \text{IoU} + \frac{\rho^2(\mathbf{c}, \mathbf{c}^{gt})}{c^2}
\label{eq:diou_loss}
\end{equation}

其中$\mathbf{c}$和$\mathbf{c}^{gt}$分别是预测框和真实框的中心点，$\rho(\mathbf{c}, \mathbf{c}^{gt})$是两中心点间的欧氏距离，$c$是包含两个边界框的最小外接矩形的对角线长度。

角点损失专门优化边界框四个角点的精确定位：
\begin{equation}
\mathcal{L}_{\text{corner}} = \frac{1}{4}\sum_{i=1}^{4} w_i \cdot \text{SmoothL1}(\Delta p_i^{\text{pred}}, \Delta p_i^{\text{gt}})
\label{eq:corner_loss}
\end{equation}

其中$\Delta p_i^{\text{pred}} = (p_i^{\text{pred}} - p_i^{\text{current}})/\sqrt{w \cdot h}$和$\Delta p_i^{\text{gt}} = (p_i^{\text{gt}} - p_i^{\text{current}})/\sqrt{w \cdot h}$分别表示预测和真实的第$i$个角点相对于当前边界框对应角点的归一化偏移量。

具体计算过程如下：给定当前边界框$(x_1, y_1, x_2, y_2)$，四个角点坐标为$p_1 = (x_1, y_1)$，$p_2 = (x_2, y_1)$，$p_3 = (x_2, y_2)$，$p_4 = (x_1, y_2)$，边界框尺寸为$w = x_2 - x_1$，$h = y_2 - y_1$。所有坐标均为像素坐标系下的绝对坐标。归一化因子$\sqrt{w \cdot h}$确保偏移量在不同尺度的边界框间具有可比性，使得损失函数对大小目标具有相同的敏感度。角点权重$w_i = 1.2$的设置旨在为角点约束提供相比标准权重1.0更强的梯度信号，增强模型对边界框精确定位的学习能力。

组合损失函数平衡全局几何一致性与局部精细定位：
\begin{equation}
\mathcal{L}_{\text{DICR}} = \lambda_{\text{diou}} \mathcal{L}_{\text{DIoU}} + \lambda_{\text{corner}} \mathcal{L}_{\text{corner}}
\label{eq:combined_loss}
\end{equation}

平衡系数设置为$\lambda_{\text{diou}} = 0.7$和$\lambda_{\text{corner}} = 0.3$，使得$\lambda_{\text{diou}} + \lambda_{\text{corner}} = 1.0$。这种7:3的权重分配使DIoU损失作为主要约束项负责全局几何一致性，角点损失作为辅助项提供局部精细化信号，在全局约束与局部优化之间取得平衡。

DICR模块包含两个1×1卷积层(192→256→64通道)进行特征降维，后接自适应平均池化和两个全连接层(64→16→4)。网络输入为RepVGG-A0提取的完整分辨率特征图，输出4维边界框偏移量$(\Delta x_1, \Delta x_2, \Delta y_1, \Delta y_2)$，表示相对于当前边界框的归一化偏移。

\subsection{系统分析与理论保证}

\subsubsection{组件协同机制与信息流分析}

MHT-RL的四个核心组件形成层次化的信息处理流水线，实现从高维感知到精确定位的端到端优化。在信息流层面，RepVGG-A0提取的12288维视觉特征与几何、动作特征拼接后，经REHM映射至4096维紧凑表示，实现约67\%的维度压缩。这种降维减少了计算复杂度，同时通过随机傅里叶特征的核近似性质在低维空间中保持了原始特征的判别能力。

MRDQN接收降维后的状态表示，通过忆阻器层的动态权重调制机制$\mathbf{W}_{\text{eff}} = \mathbf{W} \odot \sigma(\mathbf{M} \cdot \mathbf{I})$，实现对历史经验的选择性记忆。评估网络和目标网络的非对称参数设计形成"快速学习-稳定记忆"的双重机制，其中评估网络的高衰减率($\lambda_d = 0.03, 0.04$)确保对新经验的快速适应，目标网络的低衰减率($\lambda_d = 0.003, 0.002$)维持长期策略的稳定性。

TORM模块通过5步滑动窗口分析IoU变化序列，将时序行为模式转化为结构化奖励信号。其三维分解机制（方向性、强度、持续性）与IoU区间自适应系数的结合，实现了对不同学习阶段的精细化引导。训练进度权重$w_{\text{progress}} = 0.3 + \text{training\_progress} \cdot 0.7$确保趋势奖励的影响随训练深入逐步增强，避免早期训练的不稳定性。

DICR模块在强化学习完成粗定位后激活，直接使用完整分辨率特征进行亚像素级回归。DIoU损失的全局几何约束与角点损失的局部精细化优化相结合，权重比例0.7:0.3的设计平衡了全局一致性与局部精度的需求。

\subsubsection{收敛性与稳定性理论分析}

MHT-RL框架的收敛性保证来源于多个层面的理论支撑。首先，REHM基于Bochner定理的随机傅里叶特征映射在期望意义下保持核近似性质，即$\mathbb{E}[\phi(\mathbf{x})^T \phi(\mathbf{y})] = k(\mathbf{x}, \mathbf{y})$，确保降维后的特征表示不会丢失关键的判别信息。残差增强机制通过门控连接$\mathbf{z} = \phi(\mathbf{x}) + \alpha \cdot \mathbf{g} \odot \mathbf{C}(\mathbf{x})$进一步补偿可能的信息损失，其中$\alpha$的自适应调节保证了主映射与残差信息的最优平衡。

在策略学习层面，MRDQN的忆阻器机制提供了收敛性的关键保证。记忆强度矩阵$\mathbf{M}$的更新规则$\mathbf{M}_{t+1} = (1-\lambda_d)\mathbf{M}_t + \lambda_a \cdot \mathbf{A}_t \odot |\mathbf{W}_t|$确保了对重要连接的持续强化，而重要性矩阵$\mathbf{I}$基于TD误差的累积更新$\mathbf{I}_{t+1} = \mathbf{I}_t + \beta \cdot |\delta_t|$使得网络能够自适应地关注价值估计偏差较大的区域。这种双重调制机制避免了传统DQN中的灾难性遗忘，为策略的单调性改进提供了结构化保证。

TORM的趋势感知奖励机制通过抑制振荡行为进一步增强了收敛稳定性。连续改进模式的指数奖励$r_{\text{trend}} = c_{\text{improve}} \cdot s_{\text{trend}} \cdot f_{\text{steps}}$鼓励持续的正向探索，而振荡模式的负反馈$r_{\text{trend}} = -c_{\text{oscillation}} \cdot 0.5$有效抑制了局部震荡。步数因子$f_{\text{steps}} = \min(2.0, 1.0 + (n_{\text{steps}} - 3) \cdot 0.25)$的上界设计防止了奖励信号的过度放大，确保了训练过程的数值稳定性。

两阶段解耦训练策略从系统层面保证了整体框架的稳定收敛。第一阶段的强化学习专注于策略优化，避免了回归目标对探索过程的干扰；第二阶段的回归训练基于稳定的粗定位结果，确保了精确定位的可靠性。这种"先粗后精"的分治策略有效隔离了不同优化目标间的相互影响，为整体系统的收敛提供了架构层面的保证。

\subsubsection{计算复杂度与效率分析}

MHT-RL框架的计算复杂度主要由四个组件构成，总体时间复杂度为$O(H \cdot W \cdot C + D \cdot d + d^2 + T)$，其中$H \times W \times C$为输入图像维度，$D$为原始状态维度，$d$为降维后维度，$T$为平均搜索步数。

以下FLOPs分析基于单张图像推理，输入图像尺寸为$128 \times 128 \times 3$，批量大小为1的计算条件：特征提取阶段，RepVGG-A0的计算复杂度约为0.91 GFLOPs。REHM映射的复杂度为$O(D \cdot d) = O(12386 \times 4096)$，约0.05 GFLOPs，相比传统全连接层的$O(D^2) = O(12386^2)$复杂度降低了约67\%。MRDQN推理的复杂度为$O(d^2) = O(4096^2)$，约0.00 GFLOPs，忆阻器层的稀疏激活特性进一步减少了实际计算量。DICR回归的复杂度为$O(C \cdot H \cdot W) = O(192 \times 8 \times 8)$，约0.01 GFLOPs。

整体框架的总计算量约为0.97 GFLOPs。这种设计在保持检测精度的前提下，通过REHM的高效降维减少了状态空间复杂度，忆阻器层的动态权重调制实现了计算资源的自适应分配，两阶段训练策略避免了不必要的联合优化开销。

空间复杂度方面，REHM将状态表示从12386维压缩至4096维，内存占用减少约67\%。MRDQN的忆阻器状态矩阵增加了额外的存储开销，但相比于性能提升，这种开销是可接受的。整体框架在保持检测精度的前提下，实现了计算效率和存储效率的双重优化。





\section{结论}

本文提出了MHT-RL框架，通过四个关键创新组件有效解决了强化学习目标检测中的核心问题。实验结果验证了方法的有效性。

% IEEE格式致谢
\section*{致谢}

作者感谢...

% IEEE格式参考文献
\begin{thebibliography}{19}

\bibitem{ref1}
P. Tang, C. Wang, X. Wang, W. Liu, W. Zeng, and J. Wang, ``Object detection in videos by high quality object linking,'' \emph{IEEE Transactions on Pattern Analysis and Machine Intelligence}, vol. 42, no. 5, pp. 1272--1278, 2019.

\bibitem{ref2}
D. Guo, L. Tian, C. Du, \emph{et al.}, ``Suspicious object detection for millimeter-wave images with multi-view fusion siamese network,'' \emph{IEEE Transactions on Image Processing}, vol. 32, pp. 4088--4102, 2023.

\bibitem{ref3}
C. Symeonidis, I. Mademlis, I. Pitas, \emph{et al.}, ``Neural attention-driven non-maximum suppression for person detection,'' \emph{IEEE Transactions on Image Processing}, vol. 32, pp. 2454--2467, 2023.

\bibitem{ref4}
S. Ren, K. He, R. Girshick, and J. Sun, “Faster r-cnn: Towards real-time object detection with region proposal networks,” \emph{Advances in neural information processing systems}, vol. 28, 2015.

\bibitem{ref5}
K. He, G. Gkioxari, P. Dollár, \emph{et al.}, ``Mask R-CNN,'' in \emph{Proceedings of the IEEE International Conference on Computer Vision}, 2017, pp. 2961--2969.

\bibitem{ref6}
W. Liu, D. Anguelov, D. Erhan, \emph{et al.}, ``SSD: Single shot multibox detector,'' in \emph{European Conference on Computer Vision}. Springer International Publishing, 2016, pp. 21--37.

\bibitem{ref7}
K. Dong, C. Zhou, Y. Ruan, \emph{et al.}, ``MobileNetV2 model for image classification,'' in \emph{2020 2nd International Conference on Information Technology and Computer Application (ITCA)}. IEEE, 2020, pp. 476--480.

\bibitem{ref8}
J. Dong, J. Yuan, L. Li, \emph{et al.}, ``An efficient semantic segmentation method using pyramid ShuffleNet V2 with vortex pooling,'' in \emph{2019 IEEE 31st International Conference on Tools with Artificial Intelligence (ICTAI)}. IEEE, 2019, pp. 1214--1220.

\bibitem{ref9}
J. Dong, J. Yuan, L. Li, \emph{et al.}, ``An efficient semantic segmentation method using pyramid ShuffleNet V2 with vortex pooling,'' in \emph{2019 IEEE 31st International Conference on Tools with Artificial Intelligence (ICTAI)}. IEEE, 2019, pp. 1214--1220.

\bibitem{ref10}
D. Miao, Y. Wang, L. Yang, \emph{et al.}, ``Foreign object detection method of conveyor belt based on improved Nanodet,'' \emph{IEEE Access}, vol. 11, pp. 23046--23052, 2023.

\bibitem{ref11}
T. Chong, Y. Zhang, C. Ma, \emph{et al.}, ``Design and analysis of video desensitization algorithm based on lightweight model PP PicoDet,'' in \emph{2023 International Conference on Artificial Intelligence and Automation Control (AIAC)}. IEEE, 2023, pp. 121--124.

\bibitem{ref12}
M. Tan, R. Pang, and Q. V. Le, ``EfficientDet: Scalable and efficient object detection,'' in \emph{Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition}, 2020, pp. 10781--10790.

\bibitem{ref13}
Y. Xiong, H. Liu, S. Gupta, \emph{et al.}, ``MobileDets: Searching for object detection architectures for mobile accelerators,'' in \emph{Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition}, 2021, pp. 3825--3834.

\bibitem{ref14}
G. Hinton, O. Vinyals, and J. Dean, ``Distilling the knowledge in a neural network,'' \emph{arXiv preprint arXiv:1503.02531}, 2015.

\bibitem{ref15}
T. Ajanthan, P. K. Dokania, R. Hartley, \emph{et al.}, ``Proximal mean-field for neural network quantization,'' in \emph{Proceedings of the IEEE/CVF International Conference on Computer Vision}, 2019, pp. 4871--4880.

\bibitem{ref16}
J. Chen, X. Wang, and J. Zhai, ``Pruning decision tree using genetic algorithms,'' in \emph{2009 International Conference on Artificial Intelligence and Computational Intelligence}. IEEE, 2009, vol. 3, pp. 244--248.

\bibitem{ref17}
Y. Han, G. Huang, S. Song, \emph{et al.}, ``Dynamic neural networks: A survey,'' \emph{IEEE Transactions on Pattern Analysis and Machine Intelligence}, vol. 44, no. 11, pp. 7436--7456, 2021.

\bibitem{ref18}
J. C. Caicedo and S. Lazebnik, ``Active object localization with deep reinforcement learning,'' in \emph{Proceedings of the IEEE International Conference on Computer Vision}, 2015, pp. 2488--2496.

\bibitem{ref19}
M. Bellver Bueno, X. Giró-i-Nieto, F. Marqués, \emph{et al.}, ``Hierarchical object detection with deep reinforcement learning,'' in \emph{Deep Learning for Image Processing Applications}. IOS Press, 2017, pp. 164--176.

\bibitem{ref20}
Z. Jie, X. Liang, J. Feng, \emph{et al.}, ``Tree-structured reinforcement learning for sequential object localization,'' \emph{Advances in Neural Information Processing Systems}, vol. 29, 2016.

\bibitem{ref21}
G. Zuo, T. Du, and J. Lu, ``Double DQN method for object detection,'' in \emph{2017 Chinese Automation Congress (CAC)}. IEEE, 2017, pp. 6727--6732.

\bibitem{ref22}
X. Zhou, G. Han, G. Zhou, \emph{et al.}, ``Hybrid DQN-based low-computational reinforcement learning object detection with adaptive dynamic reward function and ROI align-based bounding box regression,'' \emph{IEEE Transactions on Image Processing}, 2025.

\bibitem{ref23}
Yun S, Choi J, Yoo Y, \emph{et al.}, ``Action-decision networks for visual tracking with deep reinforcement learning,`` \emph{Proceedings of the IEEE conference on computer vision and pattern recognition}. 2017: 2711-2720.

\bibitem{ref24}
Ding X, Zhang X, Ma N, \emph{et al.}, ``Repvgg: Making vgg-style convnets great again,`` \emph{Proceedings of the IEEE/CVF conference on computer vision and pattern recognition}. 2021: 13733-13742.

\bibitem{ref25}
Rahimi A, Recht B. ``Random features for large-scale kernel machines,`` \emph{Advances in neural information processing systems}, 2007, 20.

\bibitem{ref26}
Zheng Z, Wang P, Liu W, \emph{et al.}, ``Distance-IoU loss: Faster and better learning for bounding box regression,`` \emph{Proceedings of the AAAI conference on artificial intelligence.} 2020, 34(07): 12993-13000.

\bibitem{ref27}
Bochner S. Monotone funktionen, ``stieltjessche integrale und harmonische analyse.`` \emph{Mathematische Annalen}, 1933, 108(1): 378-410.

\bibitem{ref28}
Roweis S, Ghahramani Z. ``A unifying review of linear Gaussian models.`` \emph{Neural computation}, 1999, 11(2): 305-345.

\bibitem{ref31}%ref29
Haarnoja T, Zhou A, Hartikainen K, \emph{et al.} ``Soft actor-critic algorithms and applications.`` \emph{arXiv preprint arXiv:1812.05905}, 2018.


\bibitem{ref29}%ref30
Maaten L, Hinton G. ``Visualizing data using t-SNE.`` \emph{Journal of machine learning research}, 2008, 9(Nov): 2579-2605.

\bibitem{ref30}%ref31
Francesco C, De Bodt C, Verleysen M, \emph{et al.} ``Perplexity-free Parametric t-SNE.`` \emph{European Symposium on Artificial Neural Networks, Computational Intelligence and Machine Learning}. 2020.

\bibitem{ref32}
Kandel E R, Dudai Y, Mayford M R. ``The molecular and systems biology of memory.`` \emph{Cell}, 2014, 157(1): 163-186.

\bibitem{ref33}
Yger P, Gilson M. ``Models of metaplasticity: a review of concepts.`` \emph{Frontiers in computational neuroscience}, 2015, 9: 138.

\bibitem{ref34}
Zenke F, Hennequin G, Gerstner W. ``Synaptic plasticity in neural networks needs homeostasis with a fast rate detector.`` \emph{PLoS computational biology,} 2013, 9(11): e1003330.

\bibitem{ref35}
De Lange M, Aljundi R, Masana M, \emph{et al.} ``A continual learning survey: Defying forgetting in classification tasks.`` \emph{IEEE transactions on pattern analysis and machine intelligence}, 2021, 44(7): 3366-3385.

\bibitem{ref36}
Mohammed S A K, Ab Razak M Z, Abd Rahman A H, \emph{et al.} ``An efficient intersection over Union algorithm for 3D object detection.`` \emph{IEEE Access}, 2024.

\bibitem{ref37}
Zheng T, Zhao S, Liu Y, \emph{et al.} ``Scaloss: Side and corner aligned loss for bounding box regression``, \emph{Proceedings of the AAAI conference on artificial intelligence}. 2022, 36(3): 3535-3543.

\end{thebibliography}

% IEEE格式作者简介
\begin{IEEEbiography}{张三}
张三的简介在这里。
\end{IEEEbiography}

\begin{IEEEbiographynophoto}{李四}
李四的简介在这里。
\end{IEEEbiographynophoto}

\begin{IEEEbiographynophoto}{王五}
王五的简介在这里。
\end{IEEEbiographynophoto}

\end{document}





