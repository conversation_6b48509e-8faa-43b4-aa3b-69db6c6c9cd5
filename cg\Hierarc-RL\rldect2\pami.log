This is XeTeX, Version 3.141592653-2.6-0.999995 (MiKTeX 24.1) (preloaded format=xelatex 2025.8.1)  1 AUG 2025 17:49
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**d:/deeplearn/cg/Hierarc-RL/rldect2/pami.tex
(d:/deeplearn/cg/Hierarc-RL/rldect2/pami.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
(C:\Program Files\MiKTeX\tex/latex/ieeetran\IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen140
\@IEEEtrantmpdimenB=\dimen141
\@IEEEtrantmpdimenC=\dimen142
\@IEEEtrantmpcountA=\count183
\@IEEEtrantmpcountB=\count184
\@IEEEtrantmpcountC=\count185
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for TU+ptm on input line 503.
LaTeX Font Info:    No file TUptm.fd. on input line 503.

LaTeX Font Warning: Font shape `TU/ptm/m/n' undefined
(Font)              using `TU/lmr/m/n' instead on input line 503.

-- Using 8.5in x 11in (letter) paper.
-- Using DVI output.
\@IEEEnormalsizeunitybaselineskip=\dimen143
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen144
\CLASSINFOnormalsizeunitybaselineskip=\dimen145
\IEEEnormaljot=\dimen146

LaTeX Font Warning: Font shape `TU/ptm/bx/n' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1090.


LaTeX Font Warning: Font shape `TU/ptm/m/it' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1090.


LaTeX Font Warning: Font shape `TU/ptm/bx/it' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 1090.

\IEEEquantizedlength=\dimen147
\IEEEquantizedlengthdiff=\dimen148
\IEEEquantizedtextheightdiff=\dimen149
\IEEEilabelindentA=\dimen150
\IEEEilabelindentB=\dimen151
\IEEEilabelindent=\dimen152
\IEEEelabelindent=\dimen153
\IEEEdlabelindent=\dimen154
\IEEElabelindent=\dimen155
\IEEEiednormlabelsep=\dimen156
\IEEEiedmathlabelsep=\dimen157
\IEEEiedtopsep=\skip48
\c@section=\count186
\c@subsection=\count187
\c@subsubsection=\count188
\c@paragraph=\count189
\c@IEEEsubequation=\count190
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\c@figure=\count191
\c@table=\count192
\@IEEEeqnnumcols=\count193
\@IEEEeqncolcnt=\count194
\@IEEEsubeqnnumrollback=\count195
\@IEEEquantizeheightA=\dimen158
\@IEEEquantizeheightB=\dimen159
\@IEEEquantizeheightC=\dimen160
\@IEEEquantizeprevdepth=\dimen161
\@IEEEquantizemultiple=\count196
\@IEEEquantizeboxA=\box51
\@IEEEtmpitemindent=\dimen162
\IEEEPARstartletwidth=\dimen163
\c@IEEEbiography=\count197
\@IEEEtranrubishbin=\box52
) (C:\Program Files\MiKTeX\tex/latex/fontspec\fontspec.sty (C:\Program Files\MiKTeX\tex/latex/l3packages/xparse\xparse.sty (C:\Program Files\MiKTeX\tex/latex/l3kernel\expl3.sty
Package: expl3 2024-01-04 L3 programming layer (loader) 
 (C:\Program Files\MiKTeX\tex/latex/l3backend\l3backend-xetex.def
File: l3backend-xetex.def 2024-01-04 L3 backend support: XeTeX
\g__graphics_track_int=\count198
\l__pdf_internal_box=\box53
\g__pdf_backend_object_int=\count199
\g__pdf_backend_annotation_int=\count266
\g__pdf_backend_link_int=\count267
))
Package: xparse 2023-10-10 L3 Experimental document command parser
)
Package: fontspec 2022/01/15 v2.8a Font selection for XeLaTeX and LuaLaTeX
 (C:\Program Files\MiKTeX\tex/latex/fontspec\fontspec-xetex.sty
Package: fontspec-xetex 2022/01/15 v2.8a Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count268
\l__fontspec_language_int=\count269
\l__fontspec_strnum_int=\count270
\l__fontspec_tmp_int=\count271
\l__fontspec_tmpa_int=\count272
\l__fontspec_tmpb_int=\count273
\l__fontspec_tmpc_int=\count274
\l__fontspec_em_int=\count275
\l__fontspec_emdef_int=\count276
\l__fontspec_strong_int=\count277
\l__fontspec_strongdef_int=\count278
\l__fontspec_tmpa_dim=\dimen164
\l__fontspec_tmpb_dim=\dimen165
\l__fontspec_tmpc_dim=\dimen166
 (C:\Program Files\MiKTeX\tex/latex/base\fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (C:\Program Files\MiKTeX\tex/latex/fontspec\fontspec.cfg))) (C:\Program Files\MiKTeX\tex/xelatex/xecjk\xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX
 (C:\Program Files\MiKTeX\tex/latex/ctex\ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
) (C:\Program Files\MiKTeX\tex/latex/l3packages/xtemplate\xtemplate.sty
Package: xtemplate 2023-10-10 L3 Experimental prototype document functions
\l__xtemplate_tmp_dim=\dimen167
\l__xtemplate_tmp_int=\count279
\l__xtemplate_tmp_muskip=\muskip16
\l__xtemplate_tmp_skip=\skip51
)
\l__xeCJK_tmp_int=\count280
\l__xeCJK_tmp_box=\box54
\l__xeCJK_tmp_dim=\dimen168
\l__xeCJK_tmp_skip=\skip52
\g__xeCJK_space_factor_int=\count281
\l__xeCJK_begin_int=\count282
\l__xeCJK_end_int=\count283
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip53
\c__xeCJK_none_node=\count284
\g__xeCJK_node_int=\count285
\c__xeCJK_CJK_node_dim=\dimen169
\c__xeCJK_CJK-space_node_dim=\dimen170
\c__xeCJK_default_node_dim=\dimen171
\c__xeCJK_CJK-widow_node_dim=\dimen172
\c__xeCJK_normalspace_node_dim=\dimen173
\c__xeCJK_default-space_node_skip=\skip54
\l__xeCJK_ccglue_skip=\skip55
\l__xeCJK_ecglue_skip=\skip56
\l__xeCJK_punct_kern_skip=\skip57
\l__xeCJK_indent_box=\box55
\l__xeCJK_last_penalty_int=\count286
\l__xeCJK_last_bound_dim=\dimen174
\l__xeCJK_last_kern_dim=\dimen175
\l__xeCJK_widow_penalty_int=\count287

Package xtemplate Info: Declaring object type 'xeCJK/punctuation' taking 0
(xtemplate)             argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen176
\l__xeCJK_mixed_punct_width_dim=\dimen177
\l__xeCJK_middle_punct_width_dim=\dimen178
\l__xeCJK_fixed_margin_width_dim=\dimen179
\l__xeCJK_mixed_margin_width_dim=\dimen180
\l__xeCJK_middle_margin_width_dim=\dimen181
\l__xeCJK_bound_punct_width_dim=\dimen182
\l__xeCJK_bound_margin_width_dim=\dimen183
\l__xeCJK_margin_minimum_dim=\dimen184
\l__xeCJK_kerning_total_width_dim=\dimen185
\l__xeCJK_same_align_margin_dim=\dimen186
\l__xeCJK_different_align_margin_dim=\dimen187
\l__xeCJK_kerning_margin_width_dim=\dimen188
\l__xeCJK_kerning_margin_minimum_dim=\dimen189
\l__xeCJK_bound_dim=\dimen190
\l__xeCJK_reverse_bound_dim=\dimen191
\l__xeCJK_margin_dim=\dimen192
\l__xeCJK_minimum_bound_dim=\dimen193
\l__xeCJK_kerning_margin_dim=\dimen194
\g__xeCJK_family_int=\count288
\l__xeCJK_fam_int=\count289
\g__xeCJK_fam_allocation_int=\count290
\l__xeCJK_verb_case_int=\count291
\l__xeCJK_verb_exspace_skip=\skip58
 (C:\Program Files\MiKTeX\tex/xelatex/xecjk\xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
))

Package fontspec Info: Could not resolve font "SimSun/BI" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimSun/B" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimSun/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Font family 'SimSun(0)' created for font 'SimSun' with
(fontspec)             options [Script={CJK}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"SimSun/OT:script=hani;language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 

 (pami.aux)
\openout1 = `pami.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 366.
LaTeX Font Info:    ... okay on input line 366.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 366.
LaTeX Font Info:    ... okay on input line 366.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 366.
LaTeX Font Info:    ... okay on input line 366.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 366.
LaTeX Font Info:    ... okay on input line 366.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 366.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 366.
 (C:\Program Files\MiKTeX\tex/latex/base\ts1cmr.fd
File: ts1cmr.fd 2023/04/13 v2.5m Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 366.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 366.
LaTeX Font Info:    ... okay on input line 366.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 366.
LaTeX Font Info:    ... okay on input line 366.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 366.
LaTeX Font Info:    ... okay on input line 366.

-- Lines per column: 58 (exact).

Package fontspec Info: Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup4
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 366.
LaTeX Font Info:    Redeclaring math accent \acute on input line 366.
LaTeX Font Info:    Redeclaring math accent \grave on input line 366.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 366.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 366.
LaTeX Font Info:    Redeclaring math accent \bar on input line 366.
LaTeX Font Info:    Redeclaring math accent \breve on input line 366.
LaTeX Font Info:    Redeclaring math accent \check on input line 366.
LaTeX Font Info:    Redeclaring math accent \hat on input line 366.
LaTeX Font Info:    Redeclaring math accent \dot on input line 366.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 366.
LaTeX Font Info:    Redeclaring math symbol \colon on input line 366.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 366.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 366.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 366.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 366.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 366.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 366.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 366.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 366.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 366.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 366.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 366.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 366.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 366.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 366.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 366.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 366.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/lmr/m/n on input line 366.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 366.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 366.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/b/n on input line 366.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 366.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 366.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 366.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/lmr/b/it on input line 366.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 366.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 366.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <8> on input line 600.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <6> on input line 600.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <5> on input line 600.
(pami.bbl
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <7> on input line 2.
 [1


]

pami.bbl:24: LaTeX Error: Something's wrong--perhaps a missing \item.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.24 \end{thebibliography}
                          
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

) [2

] (pami.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2022/08/05>
 ***********


LaTeX Font Warning: Some font shapes were not available, defaults substituted.


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.

 ) 
Here is how much of TeX's memory you used:
 5537 strings out of 409617
 167886 string characters out of 5778277
 1951191 words of memory out of 5000000
 27585 multiletter control sequences out of 15000+600000
 560842 words of font info for 93 fonts, out of 8000000 for 9000
 1351 hyphenation exceptions out of 8191
 79i,8n,93p,816b,254s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on pami.xdv (2 pages, 43508 bytes).
