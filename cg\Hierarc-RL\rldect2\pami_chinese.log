This is XeTeX, Version 3.141592653-2.6-0.999995 (MiKTeX 24.1) (preloaded format=xelatex 2025.8.1)  2 AUG 2025 18:08
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**d:/deeplearn/cg/Hierarc-RL/rldect2/pami_chinese.tex
(d:/deeplearn/cg/Hierarc-RL/rldect2/pami_chinese.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
(C:\Program Files\MiKTeX\tex/latex/ieeetran\IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen140
\@IEEEtrantmpdimenB=\dimen141
\@IEEEtrantmpdimenC=\dimen142
\@IEEEtrantmpcountA=\count183
\@IEEEtrantmpcountB=\count184
\@IEEEtrantmpcountC=\count185
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for TU+ptm on input line 503.
LaTeX Font Info:    No file TUptm.fd. on input line 503.

LaTeX Font Warning: Font shape `TU/ptm/m/n' undefined
(Font)              using `TU/lmr/m/n' instead on input line 503.

-- Using 8.5in x 11in (letter) paper.
-- Using DVI output.
\@IEEEnormalsizeunitybaselineskip=\dimen143
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen144
\CLASSINFOnormalsizeunitybaselineskip=\dimen145
\IEEEnormaljot=\dimen146

LaTeX Font Warning: Font shape `TU/ptm/bx/n' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1090.


LaTeX Font Warning: Font shape `TU/ptm/m/it' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1090.


LaTeX Font Warning: Font shape `TU/ptm/bx/it' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 1090.

\IEEEquantizedlength=\dimen147
\IEEEquantizedlengthdiff=\dimen148
\IEEEquantizedtextheightdiff=\dimen149
\IEEEilabelindentA=\dimen150
\IEEEilabelindentB=\dimen151
\IEEEilabelindent=\dimen152
\IEEEelabelindent=\dimen153
\IEEEdlabelindent=\dimen154
\IEEElabelindent=\dimen155
\IEEEiednormlabelsep=\dimen156
\IEEEiedmathlabelsep=\dimen157
\IEEEiedtopsep=\skip48
\c@section=\count186
\c@subsection=\count187
\c@subsubsection=\count188
\c@paragraph=\count189
\c@IEEEsubequation=\count190
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\c@figure=\count191
\c@table=\count192
\@IEEEeqnnumcols=\count193
\@IEEEeqncolcnt=\count194
\@IEEEsubeqnnumrollback=\count195
\@IEEEquantizeheightA=\dimen158
\@IEEEquantizeheightB=\dimen159
\@IEEEquantizeheightC=\dimen160
\@IEEEquantizeprevdepth=\dimen161
\@IEEEquantizemultiple=\count196
\@IEEEquantizeboxA=\box51
\@IEEEtmpitemindent=\dimen162
\IEEEPARstartletwidth=\dimen163
\c@IEEEbiography=\count197
\@IEEEtranrubishbin=\box52
) (C:\Program Files\MiKTeX\tex/latex/fontspec\fontspec.sty (C:\Program Files\MiKTeX\tex/latex/l3packages/xparse\xparse.sty (C:\Program Files\MiKTeX\tex/latex/l3kernel\expl3.sty
Package: expl3 2024-01-04 L3 programming layer (loader) 
 (C:\Program Files\MiKTeX\tex/latex/l3backend\l3backend-xetex.def
File: l3backend-xetex.def 2024-01-04 L3 backend support: XeTeX
\g__graphics_track_int=\count198
\l__pdf_internal_box=\box53
\g__pdf_backend_object_int=\count199
\g__pdf_backend_annotation_int=\count266
\g__pdf_backend_link_int=\count267
))
Package: xparse 2023-10-10 L3 Experimental document command parser
)
Package: fontspec 2022/01/15 v2.8a Font selection for XeLaTeX and LuaLaTeX
 (C:\Program Files\MiKTeX\tex/latex/fontspec\fontspec-xetex.sty
Package: fontspec-xetex 2022/01/15 v2.8a Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count268
\l__fontspec_language_int=\count269
\l__fontspec_strnum_int=\count270
\l__fontspec_tmp_int=\count271
\l__fontspec_tmpa_int=\count272
\l__fontspec_tmpb_int=\count273
\l__fontspec_tmpc_int=\count274
\l__fontspec_em_int=\count275
\l__fontspec_emdef_int=\count276
\l__fontspec_strong_int=\count277
\l__fontspec_strongdef_int=\count278
\l__fontspec_tmpa_dim=\dimen164
\l__fontspec_tmpb_dim=\dimen165
\l__fontspec_tmpc_dim=\dimen166
 (C:\Program Files\MiKTeX\tex/latex/base\fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (C:\Program Files\MiKTeX\tex/latex/fontspec\fontspec.cfg))) (C:\Program Files\MiKTeX\tex/xelatex/xecjk\xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX
 (C:\Program Files\MiKTeX\tex/latex/ctex\ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
) (C:\Program Files\MiKTeX\tex/latex/l3packages/xtemplate\xtemplate.sty
Package: xtemplate 2023-10-10 L3 Experimental prototype document functions
\l__xtemplate_tmp_dim=\dimen167
\l__xtemplate_tmp_int=\count279
\l__xtemplate_tmp_muskip=\muskip16
\l__xtemplate_tmp_skip=\skip51
)
\l__xeCJK_tmp_int=\count280
\l__xeCJK_tmp_box=\box54
\l__xeCJK_tmp_dim=\dimen168
\l__xeCJK_tmp_skip=\skip52
\g__xeCJK_space_factor_int=\count281
\l__xeCJK_begin_int=\count282
\l__xeCJK_end_int=\count283
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip53
\c__xeCJK_none_node=\count284
\g__xeCJK_node_int=\count285
\c__xeCJK_CJK_node_dim=\dimen169
\c__xeCJK_CJK-space_node_dim=\dimen170
\c__xeCJK_default_node_dim=\dimen171
\c__xeCJK_CJK-widow_node_dim=\dimen172
\c__xeCJK_normalspace_node_dim=\dimen173
\c__xeCJK_default-space_node_skip=\skip54
\l__xeCJK_ccglue_skip=\skip55
\l__xeCJK_ecglue_skip=\skip56
\l__xeCJK_punct_kern_skip=\skip57
\l__xeCJK_indent_box=\box55
\l__xeCJK_last_penalty_int=\count286
\l__xeCJK_last_bound_dim=\dimen174
\l__xeCJK_last_kern_dim=\dimen175
\l__xeCJK_widow_penalty_int=\count287

Package xtemplate Info: Declaring object type 'xeCJK/punctuation' taking 0
(xtemplate)             argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen176
\l__xeCJK_mixed_punct_width_dim=\dimen177
\l__xeCJK_middle_punct_width_dim=\dimen178
\l__xeCJK_fixed_margin_width_dim=\dimen179
\l__xeCJK_mixed_margin_width_dim=\dimen180
\l__xeCJK_middle_margin_width_dim=\dimen181
\l__xeCJK_bound_punct_width_dim=\dimen182
\l__xeCJK_bound_margin_width_dim=\dimen183
\l__xeCJK_margin_minimum_dim=\dimen184
\l__xeCJK_kerning_total_width_dim=\dimen185
\l__xeCJK_same_align_margin_dim=\dimen186
\l__xeCJK_different_align_margin_dim=\dimen187
\l__xeCJK_kerning_margin_width_dim=\dimen188
\l__xeCJK_kerning_margin_minimum_dim=\dimen189
\l__xeCJK_bound_dim=\dimen190
\l__xeCJK_reverse_bound_dim=\dimen191
\l__xeCJK_margin_dim=\dimen192
\l__xeCJK_minimum_bound_dim=\dimen193
\l__xeCJK_kerning_margin_dim=\dimen194
\g__xeCJK_family_int=\count288
\l__xeCJK_fam_int=\count289
\g__xeCJK_fam_allocation_int=\count290
\l__xeCJK_verb_case_int=\count291
\l__xeCJK_verb_exspace_skip=\skip58
 (C:\Program Files\MiKTeX\tex/xelatex/xecjk\xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
)) (C:\Program Files\MiKTeX\tex/latex/graphics\graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (C:\Program Files\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks18
) (C:\Program Files\MiKTeX\tex/latex/graphics\graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (C:\Program Files\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (C:\Program Files\MiKTeX\tex/latex/graphics-cfg\graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 107.
 (C:\Program Files\MiKTeX\tex/latex/graphics-def\xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen195
\Gin@req@width=\dimen196
) (C:\Program Files\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip59

For additional information on amsmath, use the `?' option.
(C:\Program Files\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (C:\Program Files\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen197
)) (C:\Program Files\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen198
) (C:\Program Files\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count292
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count293
\leftroot@=\count294
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count295
\DOTSCASE@=\count296
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box56
\strutbox@=\box57
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen199
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count297
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count298
\dotsspace@=\muskip17
\c@parentequation=\count299
\dspbrk@lvl=\count300
\tag@help=\toks20
\row@=\count301
\column@=\count302
\maxfields@=\count303
\andhelp@=\toks21
\eqnshift@=\dimen256
\alignsep@=\dimen257
\tagshift@=\dimen258
\tagwidth@=\dimen259
\totwidth@=\dimen260
\lineht@=\dimen261
\@envbody=\toks22
\multlinegap=\skip60
\multlinetaggap=\skip61
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (C:\Program Files\MiKTeX\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (C:\Program Files\MiKTeX\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (C:\Program Files\MiKTeX\tex/latex/cite\cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
) (C:\Program Files\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)

Package fontspec Info: Could not resolve font "SimHei/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimSun/BI" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimSun/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Font family 'SimSun(0)' created for font 'SimSun' with
(fontspec)             options [Script={CJK},BoldFont={SimHei}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"SimSun/OT:script=hani;language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"SimHei/OT:script=hani;language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 

 (pami_chinese.aux)
\openout1 = `pami_chinese.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 37.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 37.
 (C:\Program Files\MiKTeX\tex/latex/base\ts1cmr.fd
File: ts1cmr.fd 2023/04/13 v2.5m Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.

-- Lines per column: 58 (exact).

Package fontspec Info: Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 37.
LaTeX Font Info:    Redeclaring math accent \acute on input line 37.
LaTeX Font Info:    Redeclaring math accent \grave on input line 37.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 37.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 37.
LaTeX Font Info:    Redeclaring math accent \bar on input line 37.
LaTeX Font Info:    Redeclaring math accent \breve on input line 37.
LaTeX Font Info:    Redeclaring math accent \check on input line 37.
LaTeX Font Info:    Redeclaring math accent \hat on input line 37.
LaTeX Font Info:    Redeclaring math accent \dot on input line 37.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 37.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 37.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 37.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 37.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 37.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 37.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/lmr/m/n on input line 37.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/b/n on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 37.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/lmr/b/it on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 37.

LaTeX Font Warning: Font shape `TU/SimSun(0)/m/it' undefined
(Font)              using `TU/SimSun(0)/m/n' instead on input line 39.


LaTeX Font Warning: Font shape `TU/SimSun(0)/m/sc' undefined
(Font)              using `TU/SimSun(0)/m/n' instead on input line 53.


LaTeX Warning: Citation `ref1' on page 1 undefined on input line 55.


LaTeX Warning: Citation `ref2' on page 1 undefined on input line 55.


LaTeX Warning: Citation `ref3' on page 1 undefined on input line 55.


LaTeX Warning: Citation `ref4' on page 1 undefined on input line 55.


LaTeX Warning: Citation `ref5' on page 1 undefined on input line 55.


LaTeX Warning: Citation `ref6' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref7' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref8' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref9' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref10' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref11' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref12' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref13' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref11' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref12' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref14' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref15' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref16' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref17' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref18' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref19' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref20' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref21' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref22' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref23' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref24' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref25' on page 1 undefined on input line 57.


LaTeX Warning: Citation `ref11' on page 1 undefined on input line 59.


LaTeX Warning: Citation `ref26' on page 1 undefined on input line 59.


LaTeX Warning: Citation `ref27' on page 1 undefined on input line 62.


LaTeX Warning: Citation `ref28' on page 1 undefined on input line 62.


LaTeX Warning: Citation `ref29' on page 1 undefined on input line 62.

[1


]

LaTeX Warning: Citation `ref30' on page 2 undefined on input line 64.


LaTeX Warning: Citation `ref31' on page 2 undefined on input line 64.


LaTeX Warning: Citation `ref32' on page 2 undefined on input line 64.


LaTeX Warning: Citation `ref33' on page 2 undefined on input line 64.


LaTeX Warning: Citation `ref34' on page 2 undefined on input line 64.


LaTeX Warning: Citation `ref35' on page 2 undefined on input line 64.


LaTeX Warning: Citation `ref36' on page 2 undefined on input line 64.


LaTeX Warning: Citation `ref37' on page 2 undefined on input line 64.

Missing character: There is no ζ in font [lmroman10-regular]:mapping=tex-text;!

LaTeX Warning: Citation `ref23' on page 2 undefined on input line 85.

LaTeX Font Info:    Trying to load font information for U+msa on input line 85.
(C:\Program Files\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 85.
 (C:\Program Files\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
Underfull \hbox (badness 10000) in paragraph at lines 103--104
[]\TU/SimSun(0)/m/it/10 智 能 体 的 目 标 是 学 习 最 优 策 略 $\OML/cmm/m/it/10 ^^Y[]\OT1/cmr/m/n/10 (\OML/cmm/m/it/10 s[]\OT1/cmr/m/n/10 ) =
 []

[2]

LaTeX Warning: Citation `ref24' on page 3 undefined on input line 108.


LaTeX Warning: Citation `ref25' on page 3 undefined on input line 110.


LaTeX Warning: Citation `ref26' on page 3 undefined on input line 116.


Underfull \vbox (badness 10000) has occurred while \output is active []



LaTeX Warning: Citation `ref28' on page 3 undefined on input line 129.


LaTeX Warning: Citation `ref31' on page 3 undefined on input line 129.


LaTeX Warning: Citation `ref29' on page 3 undefined on input line 129.


LaTeX Warning: Citation `ref30' on page 3 undefined on input line 129.


LaTeX Warning: Citation `ref25' on page 3 undefined on input line 135.


LaTeX Warning: Citation `ref27' on page 3 undefined on input line 135.

[3]
Overfull \hbox (33.64264pt too wide) detected at line 195
[][] \OT1/cmr/m/n/10 = \OML/cmm/m/it/10 Q[] \OMS/cmsy/m/n/10 ^^@ \OML/cmm/m/it/10 Q[] \OT1/cmr/m/n/10 = \OML/cmm/m/it/10 r \OT1/cmr/m/n/10 + \OML/cmm/m/it/10 ^^MQ\OT1/cmr/m/n/10 (\OML/cmm/m/it/10 s[]; [] [] Q\OT1/cmr/m/n/10 (\OML/cmm/m/it/10 s[]; a[]\OT1/cmr/m/n/10 ; \OML/cmm/m/it/10 ^^R[]\OT1/cmr/m/n/10 ); \OML/cmm/m/it/10 ^^R[]\OT1/cmr/m/n/10 ) \OMS/cmsy/m/n/10 ^^@ \OML/cmm/m/it/10 Q\OT1/cmr/m/n/10 (\OML/cmm/m/it/10 s; a\OT1/cmr/m/n/10 ; \OML/cmm/m/it/10 ^^R[]\OT1/cmr/m/n/10 )
 []


Overfull \hbox (23.13837pt too wide) detected at line 201
[]
 []


LaTeX Warning: Reference `tab:mrdqn_params' on page 4 undefined on input line 220.

d:/deeplearn/cg/Hierarc-RL/rldect2/pami_chinese.tex:227: Undefined control sequence.
l.227 \toprule
              
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

d:/deeplearn/cg/Hierarc-RL/rldect2/pami_chinese.tex:229: Undefined control sequence.
<recently read> \midrule 
                         
l.229 \midrule
              
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

d:/deeplearn/cg/Hierarc-RL/rldect2/pami_chinese.tex:234: Undefined control sequence.
<recently read> \bottomrule 
                            
l.234 \bottomrule
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

[4]

LaTeX Warning: Citation `ref22' on page 5 undefined on input line 250.


Underfull \hbox (badness 1742) in paragraph at lines 254--255
\TU/SimSun(0)/m/it/10 一 问 题，| \TU/ptm/bx/it/10 TORM \TU/SimSun(0)/m/it/10 维 护 长 度 为 \TU/ptm/bx/it/10 5 \TU/SimSun(0)/m/it/10 的 \TU/ptm/bx/it/10 IoU \TU/SimSun(0)/m/it/10 滑 动 窗 口
 []


LaTeX Warning: Citation `ref36' on page 5 undefined on input line 294.


LaTeX Warning: Citation `ref37' on page 5 undefined on input line 294.

[5] [6] [7] (pami_chinese.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2022/08/05>
 ***********


LaTeX Font Warning: Some font shapes were not available, defaults substituted.


LaTeX Warning: There were undefined references.


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.

 ) 
Here is how much of TeX's memory you used:
 7714 strings out of 409617
 205989 string characters out of 5778277
 2006191 words of memory out of 5000000
 29697 multiletter control sequences out of 15000+600000
 563914 words of font info for 119 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 79i,17n,93p,1506b,332s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on pami_chinese.xdv (7 pages, 336944 bytes).
