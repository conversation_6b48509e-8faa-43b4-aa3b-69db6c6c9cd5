IEEE TRANSACTIONS ON IMAGE PROCESSING 1 
Hybrid DQN-based Low-Computational 
Reinforcement Learning Object Detection with 
Adaptive Dynamic Reward Function and ROI 
Align-based Bounding Box Regression 
Xu<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, IEEE, <PERSON><PERSON><PERSON> , <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>tract—Deep reinforcement learning-based object detection 
approaches center around a pivotal concept: hierarchically scaling 
image segments that harbor more intricate details. Compared 
with the traditional object detection approaches, this approach 
significantly curbs the quantity of region proposals. This reduction 
holds paramount significance in curtailing the computational 
overhead. However, common deep reinforcement learning-based 
approaches suffer from a significant defect in terms of precision. 
This issue arises from inadequacies in representing image states 
appropriately and the unstable learning ability exhibited by 
the agent. To address these issues, we present the LHAR-RLD. 
First, we design the Low-dimensional RepVGG(LDR) feature 
extractor to reduce memory consumption and to reduce the 
difficulty of fitting downstream networks. Second, we propose 
the Hybrid DQN(HDQN) to enhance the agent’s ability to 
determine the state-action of images in complex environments. 
Then, the Adaptive Dynamic Reward Function(ADR) is crafted to 
dynamically adjust the reward based on shifts within the agent’s 
exploration environment. Finally, the ROI Align-based bounding 
box regression network (RABRNet) is proposed, which aims at 
further regressing the localization results of reinforcement learning 
to improve the detection precision. Our method accomplishes 
74.4% mAP on the VOC2007, 76.2% mAP on the COCO2017, 
75.2% Precision on the SF dataset, with 1.43G FLOPs. The 
precision outperforms the advanced deep reinforcement learning 
approaches and the computational cost is far lower than theirs 
and mainstream object detection methods. This method facilitates 
highly accurate object localization with minimal computational 
demands, which means it has notable applications on resourceconstrained 
devices. 
Index Terms—deep reinforcement learning, object detection, 
low-dimensional RepVGG, Hybrid DQN, adaptive dynamic reward 
function, ROI Align-based bounding box regression network. 
This work was supported in part by the National Natural Science Foundation 
of China (62303494), in part by the Natural Science Foundation of Hunan 
Province of China (2024JJ6722), in part by the Joint Funds of the National 
Natural Science Foundation of China(U22A2011), and in part by the Frontier 
Technologies R&D Program of Jiangsu(BF2024072). (Corresponding author: 
Guoxiong Zhou.) 
Xun Zhou, Guoxiong Zhou, Yongfei Xue and Aibin Chen are 
with the School of Computer Information and Engineering, Central 
South University of Forestry and Technology, Changsha 410018, 
China (e-mail: <EMAIL>; <EMAIL>; xueyongfei@ 
csuft.edu.cn; <EMAIL>). 
Guangjie Han is with the Key Laboratory of Maritime Intelligent Network 
Information Technology, Ministry of Education, Hohai University, China (email: 
<EMAIL>). 
Mingjie Lv is with the School of Automation, Central South University, 
Changsha 410083, China (e-mail: <EMAIL>). 
I. INTRODUCTION 
OBJECT detection is considered to be one of the most vital 
and challenging branches of computer vision. Object 
detection aims to locate one kind of semantic object instance. 
It has been widely used in many areas of people’s lives, for 
instance, surveillance safety [1], [2], [3] and automated driving 
[4], [5], [6]. 
As of recent years, many deep learning-based object detection 
approaches have been proposed [7], [8], [9]. Deep 
learning-based object detection methods can be broadly categorized 
into two types, two-stage [10] and one-stage [11], 
[12], [13], [14]. The classical representatives of two-stage 
detectors include R-CNN [15], Faster R-CNN [16], Mask 
R-CNN [17], etc. Two-stage detectors typically employ Region 
Proposal Networks to provide a high volume of region 
proposals, then enhance the partial features of each instance 
using RoI pooling [16] or RoI Align [17] for classification 
as well as regression. Such approaches need to pre-compute 
enormous amounts redundant region proposals. To further 
improve detection efficiency, one-stage detectors were created. 
The classical representatives of one-stage detectors include 
YOLO [18], [19], [20], SSD [21], RetinaNet [22], etc. These 
methods predict the class and location of an object directly 
from the global feature map. There is no need to crop regions 
of each instance from the original image, so inference is 
typically faster. In recent years, Transformer-based object 
detection methods have made significant progress in the field 
of computer vision. DETR [23] (DEtection TRansformer) is 
a pioneering work in this direction, which introduces the 
Transformer architecture to the object detection task for the 
first time. DETR directly predicts the category and bounding 
box of a target through the Transformer encoder-decoder 
structure directly predicts the class and bounding box of 
the target, eliminating the region proposal and non-extremely 
large value suppression steps in traditional methods and 
simplifying the detection process. Subsequently, Deformable 
DETR [24] introduces the deformable attention mechanism, 
which significantly reduces the computational complexity and 
improves the efficiency of high-resolution image processing. 
In addition, Swin Transformer [25] utilizes a hierarchical 
visual Transformer architecture to generate multi-scale feature 
representations, which further enhances the object detection 
IEEE TRANSACTIONS ON IMAGE PROCESSING 2 
performance by capturing local and global information more 
efficiently through the shifted windows mechanism. These 
innovative Transformer-based methods not only improve the 
accuracy and efficiency of object detection, but also point out 
new directions for future research. 
However, most deep learning-based approaches heavily rely 
upon convolutional neural networks to densely predict categories 
as well as exact locations of each instance. This involves 
the utilization of a large number of predefined anchors, resulting 
in considerable computational overhead [16]. Transformerbased 
object detection methods originally face high computational 
and memory requirements when processing highresolution 
images due to the self-attention mechanism needing 
to compute attention weights between all pixels. However, 
Vision Transformers (ViTs) [26] alleviate this issue by dividing 
the image into a set of fixed-size patches and processing the 
patches as tokens. This significantly reduces the computational 
overhead compared to pixel-wise attention, making it more 
feasible to handle high-resolution images. Nonetheless, the 
computational demands can still be challenging for resourceconstrained 
devices, especially as image resolution increases 
or when using very large patch sizes. 
Unlike the approaches mentioned earlier, deep reinforcement 
learning-based object detection methods explore object 
localization by training an agent equipped with sequential 
interaction capabilities [27], [28], [29]. Deep reinforcement 
learning-based object detection methods work by continuously 
adjusting the position and size of the detection frame in the 
image, where the agent obtains feedback from the environment 
and updates its strategy. Specifically, the agent takes an action 
(moving or adjusting the detection frame) in each state (image 
or image region), receives a reward (positive or negative) based 
on the precision of the detection result, and then optimizes its 
policy to maximize the cumulative reward in future detections. 
Through repeated trials, the agent eventually learns how to 
accurately detect and localize the object in images.This globalto- 
local focused object detection approach allows the agent to 
localize the object in fewer steps, so this approach typically 
necessitate fewer region proposals and exhibit reduced computational 
demands during object detection. 
Many studies have been conducted on this aspect. Caicedo 
et al. [30] introduced a reinforcement learning-based active object 
detection method employing the Deep Q Network(DQN) 
[31] to develop an action strategy for object localization. This 
approach continues searching for an object until a terminal 
action is initiated, enabling the model to localize a single object 
instance effectively, achieving high performance without 
relying on object proposals. Furthermore, Bueno et al. [32] introduced 
a hierarchical search policy founded on reinforcement 
learning principles, comprising five distinct search actions. The 
trained agent selectively attend to areas harboring substantial 
object-related messages, subsequently refining the localized 
regions for further scrutiny. This method harnesses a reinforcement 
learning agent, thereby significantly reducing the number 
of region proposals generated. Yan Wang et al. [33] proposed a 
DQN using a multi-task learning approach to localize specific 
classes of objects. The framework comprises an action execution 
agent and a terminal agent, achieving heightened average 
accuracy while minimizing the steps. Moreover, Zequn Jie 
et al. [34] integrated global interdependencies among objects 
into the object localization process, presenting a novel treestructured 
reinforcement learning methodology. This approach 
adeptly navigates the localized area, leveraging both present 
observations and historical search paths, sequentially searching 
for objects. Impressively, this method achieves comparable 
recall rates to prevalent region proposal algorithms while 
significantly reducing the number of region proposals required. 
Pirinen et al. [35] proposed a visual recognition model drl- 
RPN based on deep reinforcement learning, which replaces 
the greedy RoI selection process by a sequential attention 
mechanism trained by deep reinforcement learning. Man Zhou 
et al. [36] introduced a novel reinforcement learning-based 
anchorless object detection approach. This method involves 
a redesign of the reward function and the decoupling of 
the two-branch CNN network, resulting in notably enhanced 
performance. To address the problem of insufficient feature 
extraction, Yingjun Chen et al. [37] used the VGG16 feature 
extraction module, which incorporates the channel attention 
mechanism, as the state input for reinforcement learning. The 
distance between the center points and the aspect ratio of 
the ground truth box and the predicted box were additionally 
considered to improve the reward mechanism. The method requires 
only 4-10 candidate boxes to detect the target. However, 
these methodologies exhibit potential shortcomings. These 
include the feature extractor’s limited capability, constrained 
learning abilities within Nature DQN [38], rigidly predefined 
reward functions, and the constrained discrete action space inherent 
to reinforcement learning. Consequently, reinforcement 
learning-based object detection algorithms still possess the 
untapped potential for performance enhancement. In addressing 
issues such as inadequate image state representation and 
the fluctuating learning proficiency of reinforcement learning 
agents, and to improve the precision of object detection with 
low computational effort, this paper proposes the LHAR-RLD. 
Our primary contributions are manifested in the below listed 
categories. 
1) We propose the LDR which only uses 3*3 convolution 
and RELU to accelerate inference and minimize computation 
as well as memory consumption. 
2) We propose the HDQN to enhance the learning capabilities 
of the agent. This algorithm mitigates computational 
bias arising from max Q-value computations and speeds 
up agent’s training as well as improves agent’s reliability 
in state-action evaluation. 
3) We propose the ADR for the reinforcement learning 
object detection task. This function considers IoU and 
its variance between consecutive actions, along with 
the impact of exploration duration and can dynamically 
adjust rewards in response to changes in the exploration 
environment. 
4) We proposed the RABRNet, which employs ROI Align 
to map the object region onto the original image’s 
feature map. By achieving pixel-level alignment of the 
object zones on the feature map, it enhances agent’s 
localization regression accuracy. 
IEEE TRANSACTIONS ON IMAGE PROCESSING 3 
Memory 
Buffer 
prioritized 
replay 
s´ 
Eavl 
Hybrid Q-net 
Target 
Hybrid Q-net 
(s,a,s´) 
r 
Update parameter we 
Copy we   to wt  every x steps 
 !"" = 
1 
#$%&'(+)* , 
'-+.)/02 
# 
+=1 
+ 1 $ 1 
2$%3&(4 , 
(4$02 
2 
4=1 
'-.)/ = '5", );6-7 
'()* = * + 8 $  '5"9 , argmax)'5"9 , );6-7;6(7 
)9 = argmax)'5"9 , );6-7 
:  
:6 
s 
( = ;(", <;6< ) 
RABRNet 
(b,g) (=$ = (=> , =< )/6<     (?$ = (?> , ?< )/@<  
(6 $ 
= log(6> /6<)    (@$ = log(@> /@<) 
Update parameter wb 
:  
:6 
Hybrid 
Q-net 
crop 
s a p´ s´ 
Low-dimensional 
RepVGG 
p 
Low-dimensional 
RepVGG 
b 
(s,a,s´,r,b,g) 
Hybrid Q-net 
Training 
Testing 
a´,a´´,􀄂,an (s,bn ) d 
Output 
(s,a,r,b,g) 
a 
b 
g 
r 
Input image 
Agent Location results BBx Regression 
Shared 
weights 
(s,a,b,g) 
Shared 
weights 
RABRNet 
Adaptive 
Dynamic 
Reward Function 
b´ 
g 
Hybrid DQN 
State Transition 
Fig. 1. The framework of LHAR-RLD.The framework is mainly divided into three parts. The first part is State Transition, which represents the process of 
interaction between the agent and the environment (image); the second part is Training, where the agent obtains experience samples from the State Transition 
for training; and the third part is Testing, where the agent uses the trained network for testing. 
5) We have conducted extensive experiments to estimate 
the presented methodology in multiple scenarios. The 
experimental outcomes indicate that the LHAR-RLD 
reaches 74.4% mAP on the VOC dataset, 75.2% Precision 
on the SF dataset, with 1.43G FLOPs. The computational 
cost is far less than the advanced reinforcement 
learning approaches and mainstream object detection 
methods, as well as the precision is superior to the 
former and at the same level as the latter. 
II. METHODOLOGY 
We describe LHAR-RLD in depth throughout this section. 
A. Overall Architecture 
The general framework of the LHAR-RLD is depicted in 
Fig. 1. Low-dimensional RepVGG is the feature extraction 
network, which is a pre-trained network on ImageNet and 
does not need to be trained; Hybrid Q-net is the action 
evaluation network in the Hybrid DQN algorithm, which is 
used to fit the Q-function in reinforcement learning, where 
the input is the state of the current region with the output 
being the Q-value of nine predefined actions, with a larger 
Q-value indicating a higher probability of obtaining more 
rewards; Adaptive Dynamic Reward Functions are used to 
feedback timely rewards to the reinforcement learning agent 
as it interacts with the environment (images); RABRNet is 
the ROI Align-based bounding box regression network, which 
performs bounding box regression on the candidate regions 
localized by Hybrid DQN based on the feature vectors of the 
candidate regions when Hybrid DQN terminates the search. 
We explain the principle of LHAR-RLD in three parts: 
1) Definition of Actions: In our work, nine actions [30] 
for searching the target location are defined which act on the 
IEEE TRANSACTIONS ON IMAGE PROCESSING 4 
bounding box, as shown in Fig. 2. The first eight action change 
the position or size of the bounding box in the image by a 
certain ratio α based on the current length and width of the 
bounding box. The End action means the agent believes the 
object has been found. 
Pan 
left 
Pan 
right 
Pan 
up 
Pan 
down 
End 
Center 
bigger 
Center 
smaller 
Horizontal 
narrow 
Vertical 
narrow 
Fig. 2. Predefined candidate search actions. 
2) State Transition: For an object to be detected in a image, 
the initial current bounding box b covers the whole image. 
Firstly, the LDR is used to fetch features of the current region 
to get the state s. After that, the Hybrid Q-net selects the action 
a with the largest Q-value in order to obtain a new bounding 
box. Then, the ADR calculates the timely reward r for this step 
according to the change of the bounding box and the ground 
truth g. Then, the zone surrounded by the new bounding box 
is cropped, and the cropped image is feature extracted again 
to obtain s’. At this point, the agent finishes a state transition, 
and then a set of s, a, r, s’, b, g obtained in the process of 
each state transition are sent into the memory buffer as training 
samples. 
3) Training and Testing: DQN is an off-policy algorithm. 
When enough training samples are stored in the memory 
buffer, a fixed batch of samples will be used for training. 
Usually, DQN’s training has a set of s, a, r, s’. In order to 
jointly train the RABRNet and the Hybrid Q-net, our training 
tuple is specified as s, a, r, s’, b, g. It should be noted that 
two Hybrid Q-net networks with identical network structure 
are constructed at agent training time, which are Eval Hybrid 
Q-net and Target Hybrid Q-net respectively, and the former is 
used to predict the evaluate Q-value of each possible action 
based on the current state and decide the next action to be 
performed, and the latter is used to compute the target Qvalue 
and help update the parameters of the Hybrid Q-net. 
The parameters of the Target Hybrid Q-net are not updated 
at every step, but are only copied from the parameters of 
the Eval Hybrid Q-net every fixed number of steps or every 
fixed time interval. When training is completed, we save the 
network parameters of Eval Hybrid Q-net and RABRNet. 
During testing, the agent no longer needs feedback from the 
reward function and we load the previously saved network 
parameters into Eval Hybrid Q-net and RABRNet, and the 
agent autonomously performs state-action judgments based on 
the test input images to localize to the target step by step. 
Next, we describe the four innovations of this paper, namely 
Low-dimensional RepVGG (Section II-B), Adaptive Dynamic 
Reward Function (Section II-C), Hybrid DQN (Section II-D) 
and RABRNet (Section II-E), in detail. 
B. Low-dimensional RepVGG 
VGG is a classical convolutional neural network that has 
made tremendous achievements in the field of image recognition 
with a simple architecture consisting of convolution, 
ReLU, and pooling stack. Most of the algorithms [30], [32], 
[33], [34], [36] in current research on reinforcement learningbased 
object detection also utilize pre-trained VGG16 [39] to 
fetch image’s features. Later, with the emergence of networks 
such as Inception [40], ResNet [41], and DenseNet [42], a 
large amount of research interest shifted to elaborate structures, 
which made the models increasingly complex. Although 
network performance has improved, the shortcomings are also 
obvious. The complex branch design makes it difficult to 
apply and customize, which reduces the speed of inference. 
Some components increase the cost of memory access and 
lack suitable hardware. Complex branch design facilitates the 
training process, while its disadvantages are detrimental to 
the inference process. In response to the above problems, 
RepVGG [43] puts forward an important idea—structural 
re-parameterization, which decouples the network from the 
training to inference stages. During training, a multi-branch 
structure which is akin to ResNet-style is used. Multiple 
branches can improve the model’s representational capability, 
and during inference it is converted into VGG-style’s singlechannel 
model, the main body of the inference model only 
uses 3*3 convolution and RELU to speed up inference and 
reduce memory usage. Its parameterization consists of four 
procedures: combining Conv2d and BN, transforming 1*1 
convolution to 3*3 convolution, and BN to 3*3 convolution, 
as to merging multiple branches. The following formulas are 
applied to the convolutional and BN layers in the merging 
phase of 3*3 convolution and BN: 
Conv(x) = W(x) (1) 
BN(x) = γ × 
(x − μ) p 
σ2 
i + ε 
+ β, (2) 
where W denotes the convolution kernel weight, x denotes the 
input, 
p 
σ2 
i means the standard deviation in the BN layer, γ 
represents the ratio factor in the BN layer, and β indicates the 
bias factor in the BN layer, and ε is to prevent the introduction 
of minima introduced by dividing by zero (negligible). Then 
the integration of Conv and BN is capable to be characterized 
as: 
BN(conv(x)) = γ × 
W(x) − μ p 
σ2 
i + ε 
+ β 
= ( 
γ ×W(x) p 
σ2 
i + ε 
) + (β − 
γ × μ p 
σ2 
i + ε 
). 
(3) 
The upper equation is able to be viewed as a convolutional 
layer which contains BN operations. If the first parenthesis of 
the above equation is set to W’ and the second parenthesis is 
set to B’, then: 
W′ = 
γ ×W 
σi 
(4) 
B′ = β − 
γ × μ 
σi 
. (5) 
Eventually, it allows to be rephrased as: 
BN(conv(x)) = W′(x) + B′(x). (6) 
Reinforcement learning has a special experience replay 
training method, which requires storing the state transition 
IEEE TRANSACTIONS ON IMAGE PROCESSING 5 
… 
h 
14 layers o 
p s 
Low-dimensional RepVGG 
Flatten 
Relu +BN+ Convolution 
Adaptive Avgpooling 
Fig. 3. The network structure of Low-dimensional RepVGG. The input p 
denotes the current image region, o denotes the feature information of this 
image region, h is a history action vector, and the output s denotes the state 
of the current image region. 
samples obtained during agent exploration in a memory buffer, 
and then randomly sampling in the buffer to train the Qnetwork. 
It means that the samples stored in the memory 
buffer should not have excessive dimensionality, otherwise 
it will significantly increase the memory occupation as well 
as the fitting difficulty of the Q-network, and retard the 
speed of agent training. Therefore, based on the advantages 
of RepVGG described in the previous section, we design a 
Low-dimensional RepVGG feature extraction block to extract 
image features, as shown in the Low-dimensional RepVGG in 
Fig. 3. We use a pre-training model based on RepVGG-A0 
to extract feature from the currently observed domain. After 
the image is input into the network, the image depth features 
are extracted by a combination of stacked RELU layers, Batch 
Normalization layers and 3*3 Conv layers. In order to reduce 
the dimensionality of the output without losing too much 
information about the image features, the dimensionality of 
the output is reduced by an appropriate adaptive average 
pooling layer after stage 4 of RepVGG-A0 to reduce the fitting 
difficulty of the subsequent Hybrid Q-net and RABRNet as 
well as the burden on the memory buffer. Finally, the output 
feature information is transformed into a one-dimensional 
vector for subsequent storage in the memory buffer. This 
vector represents the feature information of the current image 
region, denoted by o, with a dimension of 5*5*1280. h is a 
fixed-size binary vector with One Hot Encoding, containing 
historical information about the ten actions performed by the 
agent during the preceding times, with a dimension of 10*9. 
Eventually, the vector o is concatenated with h to form a single 
vector representing the current state of the agent, denoted by 
s, and computed as follows 
s = AAP(Conv2D3×3(BN(Relu(xi))))n 
+ [at, at+1, ..., at+9], 
(7) 
where AAP refers to Adaptive Average Pooling, Conv2D3×3 
refers to the 3*3 Convolution, BN refers to Batch Normalization, 
Relu is a popular activation function, and xi refers to 
the i-th input, at to at+9 denote the action vectors chosen by 
the agent in the last 9 steps, t denotes the step. The details of 
the effectiveness experiments of Low-dimensional RepVGG 
can be found in Section III-D, as well as the results of the 
relevant experiments are presented in Table II and Table IV. 
C. Hybrid DQN 
Most of the reinforcement learning-based object detection 
algorithms use Nature DQN which is the standard DQN 
algorithm to learn the strategies, but Nature DQN algorithms 
are prone to problems such as Q-value overestimation, low 
sample efficiency, and unstable policy updates when dealing 
with complex tasks. Although existing improved methods such 
as Double DQN, Prioritized Experience Replay DQN and 
Dueling DQN have achieved significant results in some aspects 
respectively, their respective limitations still exist. Therefore, 
we propose to organically combine these three methods and 
utilize their complementary advantages to construct a more 
powerful reinforcement learning framework. The hybrid DQN 
we present is a deep reinforcement learning algorithm which 
synthesizes Double DQN, Prioritized Experience Replay DQN 
and Dueling DQN, aiming at overcoming the problem of Nature 
DQN algorithm and improving the reinforcement learning 
performance. 
Firstly, Hybrid DQN incorporates the idea of Double DQN, 
At the stage of calculating Q-value, changes the evaluation of 
actions from Eavl Hybrid Q-net to Target Hybrid Q-net, while 
the selection of actions is still operated by Eavl Hybrid Q-net, 
which can effectively alleviate the problem of overestimation 
that is prone to occur in Nature DQN algorithm, improve 
the stability and accuracy of learning, the detailed equation 
is shown below: 
Qeval = Q(s, a;we) (8) 
Qold 
tar = r + γ ∗ maxaQ(s′, a;wt) (9) 
Qtar = r + γ ∗ Q(s′, argmaxaQ(s′, a;we) ;wt) , (10) 
where we denotes the weight of Eavl Hybrid Q-net and wt 
denotes the weight of Target Hybrid Q-net. γ denotes the 
discount factor, which affects how much the agent value future 
rewards. 
Second, at the stage of drawing samples from the memory 
buffer, Hybrid DQN introduces the concept of Prioritized 
Experience Replay DQN, which uses the TD error as a 
prioritization index to calculate the prioritization information 
of the samples, which makes the training pay priority attention 
to those samples that are important for updating the network 
parameters. This prioritized sampling strategy makes the agent 
attend more to the samples which provide more information 
for learning, improves the sampling efficiency, makes the 
algorithm converge faster, and also increases the stability of 
training. In our task, the TD error is calculated as follows: 
TD =Qtar − Qeval 
=r + γ ∗ Q(s′, argmaxaQ(s′, a;we) ;wt) 
−Q(s, a;we) . 
(11) 
In addition, in terms of network architecture, Hybrid DQN 
adopts the network architecture of Dueling DQN, which divides 
the network structure into State branch and Value branch 
prior to network outputs, as shown in Figure 4. The use of 
this architecture allows the algorithm to better handle highly 
correlated actions, improving the efficiency and stability of 
learning. In the reinforcement learning task of this paper, to 
IEEE TRANSACTIONS ON IMAGE PROCESSING 6 
s a 
Hybrid Q-net 
Input: s = 32090 
Hidden: 1024, 1024 
Dueling: 1+9 
Outputs: a = 9 
𝑉 𝑠; 𝜃, 𝛼 
𝐴 𝑠, 𝑎; 𝜃, 𝛽 
dueling layer 
Fig. 4. The network structure of Hybrid Q-net. The input s denotes the current 
state of the image region and the output a denotes the action with the largest 
Q value computed by this network. 
address this issue of modeling the non-uniqueness of the Q and 
V values of Eq. 12 and to improve the stability of the output, 
Eq. 12 is semantically modified into Eq. 13 as follows: 
Q(s, a; θ, α, β) = V (s; θ, α) + A(s, a; θ, β) (12) 
Q(s, a; θ, α, β) = V (s; θ, α) + A(s, a; θ, β) 
− 
1 
|A| 
X 
a′ 
A(s, a′; θ, β) , (13) 
where V (s; θ, α) is the state function, which outputs a scalar, 
and A(s, a; θ, β) is the advantage function, which outputs a 
vector whose length is equal to the size of the action space; 
θ refers to the parameters of the previously fully connected 
layer; α and β are the parameters of the fully connected layer 
of the 2 branches, respectively. |A| represents the dimension of 
the dominance estimation branch A, and a′ denotes the action 
under each dimension of the dominance estimation branch A. 
In summary, Hybrid DQN combines the mitigation of overestimation 
of Double DQN, the prioritized experience replay 
of Prioritized Experience Replay DQN, and the state value and 
action’s advantage decomposition of Dueling DQN. Hybrid 
DQN fully utilizes the advantages of all three, and helping to 
improve the performance of the reinforcement learning object 
detection task. The specific algorithmic framework of Hybrid 
DQN is shown in the Training panel in Fig. 1. The details of 
the effectiveness experiments of Hybrid DQN algorithm can 
be found in Section III-D, and the related experimental results 
are shown in Table III and Table IV. 
D. Adaptive Dynamic Reward Function 
In reinforcement learning tasks, the reward function represents 
the environment’s judgment about how good or bad the 
chosen action is in a given state for directing the agent to 
acquire the favorite policy based on the current state. As the 
agent performs an action a and transitions from state s to s’, 
the environment rewards the agent in a timely fashion, and 
the reward signal indicates whether the action performed in 
the present state facilitates target localization or not. 
In our work, based on the previous reward function setting 
[30], we redesigned a more effective adaptive dynamic reward 
function. We consider the effect of the size and degree of 
change of the state transfer IoU at each step and the exploration 
time. For each step, if the IoU increases more, then this 
step should be given more reward. Meanwhile, at the beginning 
of the exploration, the agent may get the reward easily because 
the IoU is easy to increase. Nevertheless, in the late stage of 
exploration, if the IoU can still increase, an extra reward is 
given to the agent; conversely, if the IoU is still decreasing in 
the late stage of exploration, an extra penalty is given to the 
agent, informing the agent that it should not act in such a way. 
When the exploration is not terminated, i.e., the selected action 
is not a termination action, the reward function is specified as 
below: 
ξ = r + IoU (b′, g) − IoU (b, g) + ζt (14) 
Rm (a, s → s′) = 
( 
ξ, if IoU (b′, g) > IoU (b, g) 
−ξ, otherwise. 
(15) 
Where r is the base reward value, set to 1, ζ is the 
equilibrium coefficient of exploration time, set to 0.01, and t is 
the the number of steps within an episode which can represent 
the exploration time, an integer in the range [1, 50]. When the 
exploration is terminated, the reward function is specified as 
below: 
Rt (a, s → s′) = 
( 
η + IoU (b′, g) , if IoU (b′, g) ≥ τ 
−η − IoU (b′, g) , otherwise. 
(16) 
Where η is fixed at 3, τ is set to 0.9, which is the IoU 
threshold. 
The details of the effectiveness experiments of Adaptive 
Dynamic Reward Function can be found in Section III-D, and 
the results of the relevant experiments are presented in Table 
IV. 
E. ROI Align-based Bounding Box Regression Network 
Deforming the bounding box to the specific proportions of 
the current area is a relatively rough way of object localization, 
and it is difficult to completely cover some sizeable ojcects, 
so there continues to be potential for refinement in terms of 
precision. Therefore, this paper proposes a ROI Align-based 
bounding box regression network RABRNet, which is used 
to further fine-tune agent’s localization results to improve the 
precision of localization. 
In previous research on reinforcement learning object detection, 
there have been some scholars who have combined 
reinforcement learning algorithms with bounding box regression. 
However, in these studies, the common practice is to 
directly input the one-dimensional state vectors of the current 
region directly into the fully connected layer to perform 
regression. Although it is convenient and quick to do so, 
the one-dimensional state vectors directly input into the fully 
connected layer will completely lose the spatial and positional 
information of the current region, which is not conducive to the 
subsequent regression. To address these problems, the structure 
of the bounding box regression network introduced in this article 
is illustrated in Fig. 5. After the end of agent localization, 
we align the feature information of the whole raw image and 
IEEE TRANSACTIONS ON IMAGE PROCESSING 7 
s d 
RABRNet 
RoI Align 
image feature 
5*5*1280 
current region 
Feature 
5*5*1280 
32000 
1024 1024 
4 
Flatten 
fully connected layers 
b 
Fig. 5. The network structure of RABRNet. Input s denotes the state of the 
original image to which the current image region corresponds, input b denotes 
the positional coordinates of the current image region on the original image, 
and output d denotes the offset of the positional coordinates computed by the 
network. 
with the boundary coordinates of the agent localization result 
on the original map with pixel-level accuracy through RoI 
Align to obtain a feature map with a fixed size of 5*5*1280 
for the corresponding position. Then pass it into the fully 
connected layer, and finally obtain a vector with dimension 4, 
which represents the search box in the horizontal coordinates 
, vertical coordinate, width, and height adjustment offsets, 
respectively. Then through the border regression coordinate 
parameterization formula [16] (refer to Eq. 17), the regressed 
search bounding box coordinates t∗j = [t∗ 
x, t∗y, t∗ 
w, t∗ 
h] are 
obtained, and then the loss is calculated with the GT to update 
the parameters of the regression network. 
t∗ 
x = 
xg − xb 
wb 
, t∗ 
y = 
yg − yb 
hb 
, 
t∗ 
w = log ( 
wg 
wb 
), t∗ 
h = log ( 
hg 
hb 
). 
(17) 
The RABRNet is effective in addressing the issue that the 
discrete action space predefined by reinforcement learning 
struggles to fully encompass the target size space and improve 
the detection precision. The details of the effectiveness 
experiments of RABRNet can be found in Section III-D, and 
the relevant laboratory results are presented in Table IV. 
Algorithm 1 lists the specific training steps of the LHARRLD. 
III. EXPERIMENT 
In this section, we begin by introducing our dataset, experimental 
environment, and evaluation metrics. Then, we 
present ablation studies to investigate our algorithm. Finally, 
we compare our LHAR-RLD on different datasets to several 
of the most advanced reinforcement learning-based object 
detection approaches. 
A. Dataset 
The datasets utilized in this paper are the PASCAL Visual 
Object Classes (VOC) 2007 and 2012 datasets [44], both of 
which consist of images of 20 object classes, such as people, 
birds, cats, cows, and dogs. Specifically, we uses the combined 
VOC2007 and VOC2012 trainval dataset for training, which 
contains 16551 images, and the VOC2007 test dataset for 
Algorithm 1 LHAR-RLD. 
Initialize replay memory D to capacity N 
Initialize evaluate action-value function Q with random 
weights ωe 
Initialize target action-value function bQ with weights ωt = 
ωe 
Initialize bounding box regression function f with weights 
ωb 
For epoch = 1, M do 
For xi in the training set 
 
xi 
	n 
i=1 do 
Initialize bounding box b=[0,W,0,H], ground truth g 
Calculate s by Eq. (7) 
For t = 1, T do 
With probability ϵ select a random action at 
otherwise select at = maxaQ∗(st, a; ωe) 
Execute action at and observe bt+1, rt, xi 
t+1, st+1 
Store transition ( st, at, rt, st+1, b, g) in D 
Calculate Priority by Eq. (11) 
and sample minibatch of transitions from D 
Calculate yj = 
( 
rj , if end 
Eq. (10), otherwise. 
Calculate t∗ 
k by Eq. (17) 
Perform a gradient descent step on Loss in Fig. 1 
with respect to the network parameters ωe and ωb 
Every C steps reset bQ = Q 
end for 
end for 
end for 
testing, which contains 4952 images. Moreover, in this paper, 
experiments are conducted on the Microsoft Common Objects 
in Context (MS COCO) dataset. The MS COCO dataset is 
a large-scale object detection, segmentation, and captioning 
dataset. It contains over 330,000 images, with more than 1.5 
million object instances annotated across 80 object categories. 
Here, we only consider a single specific class of objects for 
detection, and we use images containing objects from one class 
at a time to train the model. 
In addition, the SF dataset [45] is utilized in this article.The 
SF dataset contains 12,620 synthesized smoke images with 
different background disturbances and shapes, all of which 
have a resolution of 1920*1080. In this paper, this dataset is 
preprocessed to generate a 224 × 224 dataset with a train set 
size that is 2500 and a test set size that is 500 by random 
selection and clipping. 
B. Experimental Environment 
All the experiments conducted in this investigation were carried 
out in the identical hardware and software environments 
in order to examine the properties of the LHAR-RLD. The 
specific experimental environment settings are shown in Table 
I. During training, all the image sizes in the dataset as well as 
the image size of the post-cut region explored by each agent 
are set to 224×224 pixel2. 
IEEE TRANSACTIONS ON IMAGE PROCESSING 8 
TABLE I 
EXPERIMENTAL ENVIRONMENT SETTING 
Operating System Ubantu18.04 
Hardware environment 
Intel Core I9 processor 
500G Storage 
NVIDIA RTX 3090 GPU 
24G video memory 
Software environment 
CUDA Toolkit 11.4 
CUDNN V7.4.2 
Python 3.7 
Pytorch-GPU 1.12.1 
C. Evaluation Metrics 
The complete term for IoU stands for “Intersection over 
Union”, which is a frequently utilized concept in object 
detection. IoU is computed as the intersection ratio between 
the “predicted bounding box” with the “real bounding box”. 
Average IoU is the average intersection over union ratio, which 
is calculated as follows: 
Average IoU = 
1 
N 
∗ 
Xn 
i=1 
IoUi, (18) 
where N denotes the population of all samples; IoUi indicates 
the IoU calculated from the test result obtained for the i-th 
sample and the actual result. Precision refers to the ratio of TP 
in the recognized images. The calculation formula of Precision 
goes along the following lines: 
Precision = 
TP 
TP + FP 
, (19) 
where TP denotes the correctly recognized sample whose 
IoU > 0.5; TP+FP is the aggregate amount of images output 
by the detection model. 
FLOPs refers to the number of floating-point operations, 
which can be interpreted as the computational volume and 
considered as a metric of the sophistication of the model. 
To compute the FLOPs of the model, we assume that the 
convolution is materialized in the form of a sliding window 
as well as that the nonlinear function is cost free to calculate. 
As for the convolution kernel, we derive: 
FLOPs = 2HW 
􀀀 
CinK2 + 1 
 
Cout, (20) 
where H, W, and Cin are the height, width, and frequency 
of channels feeding the feature map, K is the kernel breadth 
(assuming symmetry), and Cout indicates the amount of output 
channels. In the case of the fully-connected layer, our FLOPs 
calculation formula is: 
FLOPs = (2I − 1)O, (21) 
where I denotes the input dimension as well as O represents 
the output dimension. 
FPS is a crucial indicator to measure the detection speed. 
FPS can be calculated as 
FPS = 
1 
t 
, (22) 
where t is the time cost required to process each picture frame. 
D. Ablation Study 
1) Selection of Feature Extractor 
In order to verify the effectiveness of LDR module, we 
replaced many advanced feature extractors for the experiments, 
such as VGG16 [39], Resnet50 [41], ConvNeXt Tiny [46], 
Swin Transformer [25] Tiny which are advanced feature extraction 
networks. All these networks were loaded with official 
pre-training parameters and then accessed to our reinforcement 
learning downstream for training. The brackets after the 
feature extractors indicate the dimensionality of the output. 
The final experimental results are shown in Table II. The 
experimental results show that our proposed LDR Average 
IoU reaches 66.7% and Precision reaches 88.4%, which are 
both higher than the other four feature extractors. the FLOPs 
are only 1.43G, which are both much lower than the others, 
and the computational cost is lower. Such experimental results 
are inextricably related to both the network structure as well 
as the output dimension of the network. Deeper networks and 
more branching structures would not necessarily be applicable 
in this task. In this paper, it is found that the appropriate 
output dimension is the most important in this task. Resnet50 
has poorer final results due to its excessive output dimension 
which causes it to have difficulty converging in deeper Qnetworks 
downstream of the access. ConvNeXt Tiny and Swin 
Transformer Tiny also had poor results because their output 
dimensions were too low, resulting in too little information 
about the image features contained, making it difficult for the 
downstream deep Q-network to learn the precise actions. The 
LDR proposed in this paper has a proper output dimension, 
which contains rich feature information and can converge in 
the downstream task to achieve better results. 
TABLE II 
COMPARISON OF DIFFERENT FEATURE EXTRACTORS 
Feature Extractor Average IoU(%) Precision(%) FLOPs(G) 
VGG16(7*7*512) 66.6 83.7 15.48 
Resnet50(7*7*2048) 54.8 68.4 3.83 
ConvNeXt-T(1*1*768) 59.2 77.3 4.53 
swin-T(1*1*768) 62.1 79.5 4.53 
LDR(5*5*1280) 66.7 88.4 1.43 
2) Decomposition of HDQN Modules 
In order to highlight the advancement of HDQN, we decompose 
the HDQN modules as shown in the Table III. In 
the Table, 1 denotes Double DQN, 2 denotes Prioritized 
Experience Replay DQN, and 3 denotes Dueling DQN. 
Since these three improved DQN are enhancements to different 
modules of the Nature DQN, they can be combined in a 
straightforward way. The experimental results demonstrate the 
effectiveness of HDQN. Out of the seven combinations of 
these three DQN algorithms, the best results were achieved by 
combining all three together, with Average IoU of 66.7% and 
precision of 88.4%. It is obvious from the table that Double 
DQN is the most effective, this is because the standard DQN 
algorithm (Nature DQN) obtains the target Q-value directly 
through the greedy method. The greedy method converges 
the Q-value to the possible optimization target quickly by 
maximization, but it tends to lead to the problem of overIEEE 
TRANSACTIONS ON IMAGE PROCESSING 9 
Image Ground truth Backbone Backbone+LDR 
Backbone+ 
LDR+HDQN 
Backbone+ 
HDQN+ADR 
+RABRN 
Backbone+ 
LDR+HDQN+ 
ADR+RABRN 
Backbone+ 
LDR+HDQN 
+RABRN 
Backbone+ 
LDR+HDQN 
+RABRN 
(a) 
(b) 
(c) 
(d) 
(e) 
Backbone+ 
LDR+ADR 
+RABRN 
Fig. 6. Qualitative results achieved by different combinations.The green bounding box indicates the label, the red bounding box indicates the detection result 
of the corresponding method, and the number in its upper left corner indicates the IoU value. 
(a) (b) 
Fig. 7. Figure (a) represents the reward curve for 50 epochs of training on the aeroplane class of the VOC trainval dataset for both the LHAR-RLD with 
Hybrid DQN and with Nature DQN cases, and Figure (b) represents the training time curve for 50 epochs of training on the aeroplane class of the VOC 
trainval dataset for both the LHAR-RLD with Hybrid DQN and with Nature DQN cases. 
estimation of the Q-value, which makes the model have a large 
deviation. The Double DQN algorithm decouples the selection 
of actions and the calculation of target Q value, which can 
solve the problem of overestimation of Q value well. 
TABLE III 
DECOMPOSITION OF HDQN MODULES 
DQN Algorithm Average IoU(%) Precision(%) 
/ 63.7 81.4 
1 65.2 84.3 
2 64.3 82.9 
3 64.7 83.1 
1 + 2 64.4 85.7 
1 + 3 65.6 86.2 
2 + 3 63.2 81.6 
1 + 2 + 3 (HDQN) 66.7 88.4 
3) Integral Ablation Study 
A sequence of ablation experiments is conducted in order to 
evaluate the validity of our proposed method exemplified by 
the aeroplane class in VOC2007 test. The training epoch is set 
to 50, the batch size is set to 128, and all the algorithms are 
trained in the same environment to ensure the reliability and 
consistency of the experiments. In the identical experimental 
settings, only the structural composition in the algorithms is 
changed to compare the detection performance of the algorithms. 
The backbone utilizes VGG16 as the feature extractor, 
Nature DQN as the strategic learner, fixed reward function 
[30] to guide the agent, and does not incorporate bounding 
box regression.We compare the algorithms’s performance by 
replacing or adding one or more modules from LDR, HDQN, 
ADR, and RABRNet based on the backbone. 
Fig. 6 and Table IV show the impact of each component on 
the algorithm from qualitative and quantitative perspectives, 
respectively. The experimental findings clearly indicate that 
each of our presented modules contributes to improving the 
algorithms’ performance, proving the effectiveness of each 
IEEE TRANSACTIONS ON IMAGE PROCESSING 10 
TABLE IV 
Average IoU, Precision AND FLOPs VALUES FOR DIFFERENT COMBINATIONS ON THE AEROPLANE IN VOC2007 test 
Backbone LDR HDQN ADR RABRNet Average IoU(%) Precision(%) FLOPs(G) 
√ 
√ √ 63.7 81.4 15.45 
√ √ √ 64.1 83.7 1.40 
√ √ √ √ 64.2 84.5 1.40 
√ √ √ √ 64.8 85.3 1.40 
√ √ √ √ 66.6 83.7 15.48 
√ √ √ √ 65.1 84.5 1.43 
√ √ √ √ √ 65.2 86.8 1.43 
66.7 88.4 1.43 
TABLE V 
COMPARISON ON THE VOC DATASET 
VOC2007 test 
Method Category ID mAP(%) FLOPs(G) FPS 
1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 
Caicedo-RL 55.9 61.9 38.4 36.5 21.4 56.5 58.8 55.9 21.4 40.4 46.3 54.2 56.9 55.9 45.7 21.1 47.1 41.5 54.7 51.4 46.1 15.45 12 
Hierarc-RL 28.3 30.1 24.1 20.6 17.3 31.0 28.3 44.4 17.8 15.1 30.0 37.6 33.9 36.0 41.1 19.1 11.3 40.8 38.6 15.7 28.1 15.41 27 
Multitask-RL 59.2 62.3 40.2 41.2 24.1 59.5 67.1 55.6 24.1 64. 50.2 54.1 57.1 54.7 46.5 29.4 48.5 44.2 54.1 35.9 48.6 15.49 4 
Tree-RL 71.2 82.4 72.0 62.3 50.4 80.0 79.3 83.4 57.8 79.3 72.0 82.7 83.3 77.2 77.2 44.4 76.4 76.5 82.2 71.5 73.1 15.52 14 
drl-RPN 79.7 81.7 76.2 69.1 54.1 82.0 87.8 84.6 60.6 83.9 73.7 84.1 86.3 80.6 80.6 49.2 77.8 75.1 83.0 76.0 76.3 16.90 2 
ReinforceDet 76.5 82.0 74.8 65.2 52.3 80.8 81.1 85.7 56.6 80.3 72.4 83.2 83.4 76.3 77.1 42.2 77.3 74.8 82.4 70.2 73.7 15.50 11 
Chen-RL 68.8 70.5 52.6 43.7 31.6 63.1 68.9 59.9 36.1 54.1 57.8 66.4 68.8 68.3 57.6 32.0 58.2 46.4 66.4 64.5 56.8 15.66 12 
LHAR-RLD(Ours) 88.4 84.1 74.5 62.1 56.5 82.7 90.7 85.9 39.3 66.7 90.0 84.2 89.5 71.8 84.4 41.2 76.0 75.9 78.9 58.5 74.4 1.43 10 
module. 
As illustrated in Fig. 6(a)(b)(c)(d), the final LHAR-RLD 
algorithm has more accurate localization results for both large, 
medium-sized, and small objects; as shown in Fig. 6(e), the 
LHAR-RLD can also localize more accurately for objects with 
fuzzy features. In addition, Fig. 7 illustrates how HDQN is 
ultimately able to improve precision. The method not only 
reduces training time and speeds up learning, but also enables 
higher rewards to be obtained more quickly. 
Finally, with the simultaneous utilization of the four modules 
LD-RepVGG, HDQN, ADR , RABRNet in Backbone, 
Average IoU reaches 66.7%, Precision reaches 88.4%, FLOPs 
reaches 1.43G, and the algorithm achieves the highest performance. 
E. Comparison Experiment 
1) Comparison on VOC Dataset 
To further demonstrate the capability and effectiveness of 
LHAR-RLD, we conducted experiments under 20 categories 
of VOC2007 test and compared them with advanced reinforcement 
learning object detection approaches, including 
Caicedo-RL [30], Hierarc-RL [32], Multitask-RL [33], Tree- 
RL [34], drl-RPN [35] and ReinforceDet [36]. The combined 
VOC2007 and VOC2012 train and val dataset is utilized to 
train, and the VOC2007 test dataset is utilized to test. The 
training epoch is set to 50 and all the algorithms are trained in 
the same environment to ensure the reliability and consistency 
of the experiments. The 1-20 in Table V represent: aeroplane, 
bicycle, bird, boat , bottle, bus, car, cat, chair, cow, diningtable, 
dog, horse, motorbike, person, pottedplant, sheep, sofa, train, 
vmonitor. The number under each Category ID indicates the 
precision obtained from this category test. 
The findings of the comparative experiments are presented 
in Table V, where LHAR-RLD achieves 74.4% mAP, 1.43G 
FLOPs and 10 FPS, outperforming the advanced reinforcement 
learning object detection approaches. First, the FLOPs of our 
method are only 1.43G, which is 10 times lower than the advanced 
reinforcement learning methods. Second, LHAR-RLD 
obtains the highest precision with the lowest computational 
effort on ten categories: aeroplane, bicycle, bottle, bus, car, cat, 
diningtable, dog, horse, and person. Third, the LHAR-RLD 
also achieves a decent level of precision in the remaining ten 
categories. On the metric of mAP, drl-RPN outperforms our 
method, mainly because drl-RPN is not a pure reinforcement 
learning method. Its first stage is implemented by replacing 
the greedy RoI selection process with a sequential attention 
mechanism trained through deep reinforcement learning. It 
also has a object detector in its second stage. So it is reasonable 
that it outperforms our method in mAP metrics, but at the same 
time, it has a relatively large disadvantage in FLOPs and FPS 
compared to our method. In the metric of FPS, the FPS of 
our method is 10, which is not advantageous compared to 
other methods, and this point is still deficient. This is mainly 
attributed to the high output dimension of the feature extractor, 
which leads to an increase in the input dimension of the 
downstream reinforcement learning Q-network, slowing down 
the speed. There is still a need to improve on this point in 
future research. 
2) Comparison on COCO Dataset 
In order to better validate the robustness of the algorithm, 
we conducted comparative experiments on the COCO dataset, 
which has a larger, richer and more complex number of 
images. We selected five classes in the COCO dataset for 
training, which are airplane, person, dog, cat, and bird, and 
IEEE TRANSACTIONS ON IMAGE PROCESSING 11 
the experimental settings are as above. It should be noted that 
since the method proposed in this paper is the single-case 
detection modality, only single object images are selected for 
detection. 
TABLE VI 
COMPARISON ON THE COCO DATASET 
COCO2017 
Method Category mAP(%) FLOPs(G) FPS 
person airplane dog cat bird 
Caicedo-RL 45.3 50.9 56.2 58.3 49.8 52.1 15.45 12 
Hierarc-RL 22.7 36.8 34.5 26.9 27.8 29.7 15.41 27 
Multitask-RL 32.2 40.3 42.8 47.9 43.8 41.4 15.49 4 
Tree-RL 50.5 79.8 76.2 74.9 72.1 70.7 15.52 14 
drl-RPN 63.4 81.7 83.0 80.2 74.6 76.6 16.90 2 
ReinforceDet 58.6 80.5 79.6 76.4 75.3 74.1 15.50 11 
Chen-RL 48.7 62.3 66.4 67.1 58.5 60.6 15.66 12 
LHAR-RLD(Ours) 61.5 82.9 81.8 77.3 77.4 76.2 1.43 10 
It is clear from Table VI that our proposed method achieves 
76.2% mAP with 1.43 GFLOPs and 10 FPS on the five 
categories of the COCO2017 dataset. The mAP of our method 
is 2.2% higher than that of ReinforceDet and just 0.4% lower 
than that of drl-RPN. In terms of FLOPs, the computational 
effort of our method is much lower than other methods. 
This shows that even on the COCO dataset, which has more 
complex images and more heterogeneous samples, our method 
can achieve quite high precision. 
3) Comparison on SF dataset 
Additionally, we performed comparative experiments on the 
SF dataset, which is a smoke dataset. In the same experimental 
environment, we used LHAR-RLD on this dataset after 30 
epochs of training to compare it with the advanced reinforcement 
learning object detection approaches. The experimental 
results obtained from the tests are presented in Table VII, some 
of the visualization results are presented in Figure 8. 
Table VII displays the comparative findings of these methods 
over the SF dataset. It is observed that our methods also 
show superior performance on the SF dataset. The quantitative 
results show that method we present is superior to other 
reinforcement learning approaches on the SF dataset in terms 
of Average IoU, Precision, and our Average IoU score reaches 
58.1%, Precision score reaches 75.2%. In terms of FLOPs, our 
TABLE VII 
COMPARISON ON SF DATASET 
SF dataset 
Method Average IoU(%) Precision(%) FLOPs(G) FPS 
Caicedo-RL 39.2 41.0 15.45 12 
Hierarc-RL 17.8 2.41 15.41 27 
Multitask-RL 24.6 18.4 15.49 4 
Tree-RL 48.5 60.2 15.52 14 
drl-RPN 56.3 74.7 16.90 2 
ReinforceDet 50.1 62.4 15.50 11 
Chen-RL 44.7 57.2 15.66 12 
LHAR-RLD(Ours) 58.1 75.2 1.43 10 
method is also much lower than other reinforcement learning 
methods, demonstrating the low computational effort of our 
method. 
The visualized comparison results over the SF dataset are 
presented in Fig. 8. it is clear that for smokes with different 
background interference and shape, LHAR-RLD can localize 
smoke very precisely, which is better than other reinforcement 
learning algorithms. However, in practical applications, smoke 
detection usually requires relatively fast detection speed, while 
LHAR-RLD, as a reinforcement learning object detection 
algorithm, searches for the object location by training an agent 
with sequential interaction ability, which sacrifices a certain 
detection speed to achieve higher localization precision, and 
has slower detection speed compared with the mainstream 
smoke detection algorithms [47], [48], [49], [50], [51]. 
IV. DISCUSSION 
In addition to comparing with the advanced reinforcement 
learning approaches, we also make a comparison of our 
approach with prevailing object detection approaches, which 
include Faster R-CNN [16], YOLOv5s, RTMDet [52] and 
DETR [23]. To ensure the validity of the experiments, these 
methods were also trained on a single category. This experimental 
setting is the same as the previous comparison 
experiment on VOC dataset. 
Table VIII shows the comparison results of these methods 
on the VOC2007 test . From the table, the mAP of our 
method is slightly lower than these advanced object detection 
Image Ground truth Caicedo-RL Hierarc-RL Multitask-RL Tree-RL ReinforceDet LHAR-RLD 
drl-RPN Chen-RL 
Fig. 8. Qualitative results achieved by different reinforcement learning-based object detection methods on SF dataset. 
IEEE TRANSACTIONS ON IMAGE PROCESSING 12 
TABLE VIII 
COMPARISON WITH MAINSTREAM OBJECT DETECTION METHODS 
VOC2007 test 
Method mAP(%) FLOPs(G) FPS 
Faster R-CNN(VGG16) 82.6 19.53 15 
YOLOv5s 79.3 16.44 170 
RTMDet-s 76.8 1.69 311 
DETR(ResNet-50) 77.3 14.55 35 
LHAR-RLD(Ours) 74.4 1.43 10 
methods. Meanwhile, the FLOPs of our method are significantly 
lower than the other methods, and are at the same 
level as the real-time detector RTMDet-s, which is mainly 
due to the fact that our method uses RepVGG-A0 in the 
feature extraction stage, which minimizes the computational 
complexity while maintaining good performance by using a 
simple but effective convolutional block structure. Moreover, 
in terms of FPS metrics, our method has a drawback, which 
is mainly due to the fact that reinforcement learning target 
detection, as a sequential decision-making task, usually 
requires multiple forward steps to be performed, although 
the resource consumption of each step is very low, which 
limits its detection speed. In our subsequent work we will 
conduct research on multi-agent reinforcement learning and 
introduce multi-agent reinforcement learning into our method 
to improve the detection efficiency. In summary, Our method 
attains a comparable level of detection precision to mainstream 
object detection algorithms, showcasing remarkable efficiency 
with minimal computational requirements. The LHAR-RLD 
algorithm significantly mitigates detection costs and environmental 
impact, enabling deployment on devices with limited 
computational capabilities. This expansion broadens the scope 
of applications and scenarios for object detection. 
V. CONCLUSION 
Despite advancements, deep reinforcement learning-based 
object detection algorithms face performance limitations stemming 
from several factors. These include the feature extractor’s 
inadequate feature extraction capabilities, the constrained 
learning potential of Nature DQN, rigidly fixed reward functions, 
and the lack of flexibility in the predefined discrete 
action space within reinforcement learning frameworks. To 
solve these problems, we proposes LHAR-RLD. First, we 
design the LDR to reduce the memory consumption and to 
reduce the difficulty of fitting downstream networks. Second, 
we propose the HDQN to promote the agent’s ability to 
determine the state-action of images in complex environments. 
Then, the ADR is crafted to dynamically adjust the reward 
value based on shifts within the agent’s exploration environment. 
Finally, the RABRNet is proposed to regress the 
agent’s localization results more accurately, which to some 
extent solves the problem that the predefined discrete action 
space of reinforcement learning is difficult to cover the object 
size space completely. The method achieves 74.4% mAP on 
the VOC2007 test, and 75.2% Precision on the SF dataset 
with 1.43G FLOPs, the computational cost is far less than 
the advanced deep reinforcement learning approaches and 
mainstream object detection methods, while the precision is 
better than the former and at the same level as the latter. 
Moving forward, we will further refine the LHAR-RLD in 
the following aspects: (1) The robustness of the method of 
selecting the next explored region based on the aspect ratio 
of the candidate region still needs to be improved. We can 
try to design a more robust action to make the reinforcement 
learning agent more flexible in searching for objects and 
improve the detection speed. (2) Most of the object detection 
methods based on reinforcement learning are the single-case 
detection modality, and the method proposed in this paper is 
no exception. This is caused by the principle of reinforcement 
learning, which can only localize one object in one episode. 
Further, we will investigate how to combine reinforcement 
learning with multi-case detection. (3) Reinforcement learning 
methods can continue to expand. Combining Value-based and 
Policy-based methods can further enrich the model. 
REFERENCES 
[1] D. Guo, L. Tian, C. Du, P. Xie, B. Chen, and L. Zhang, “Suspicious 
object detection for millimeter-wave images with multi-view fusion 
siamese network,” IEEE Trans. Image Process., vol. 32, pp. 4088–4102, 
2023. 
[2] W.-Y. Hsu and W.-Y. Lin, “Ratio-and-scale-aware yolo for pedestrian 
detection,” IEEE Trans. Image Process., vol. 30, pp. 934–947, 2021. 
[3] C. Symeonidis, I. Mademlis, I. Pitas, and N. Nikolaidis, “Neural 
attention-driven non-maximum suppression for person detection,” IEEE 
Trans. Image Process., vol. 32, pp. 2454–2467, 2023. 
[4] R. Qian, X. Lai, and X. Li, “3d object detection for autonomous driving: 
A survey,” Pattern Recognition, vol. 130, p. 108796, 2022. 
[5] Y. Wang, Q. Mao, H. Zhu, J. Deng, Y. Zhang, J. Ji, H. Li, and Y. Zhang, 
“Multi-modal 3d object detection in autonomous driving: a survey,” 
International Journal of Computer Vision, pp. 1–31, 2023. 
[6] J. Ahmad and A. Del Bue, “mmfusion: Multimodal fusion for 3d objects 
detection,” arXiv preprint arXiv:2311.04058, 2023. 
[7] S. Chen, P. Sun, Y. Song, and P. Luo, “Diffusiondet: Diffusion model 
for object detection,” in Proc. IEEE Int. Conf. Comp. Vis., 2023, pp. 
19 830–19 843. 
[8] Y. Luo, X. Cao, J. Zhang, J. Guo, H. Shen, T. Wang, and Q. Feng, “Cefpn: 
Enhancing channel information for object detection,” Multimedia 
Tools and Applications, vol. 81, no. 21, pp. 30 685–30 704, 2022. 
[9] G. Cheng, J. Wang, K. Li, X. Xie, C. Lang, Y. Yao, and J. Han, 
“Anchor-free oriented proposal generator for object detection,” IEEE 
Trans. Geosci. Remote Sens., vol. 60, pp. 1–11, 2022. 
[10] W. Li, Z. Chen, B. Li, D. Zhang, and Y. Yuan, “Htd: Heterogeneous task 
decoupling for two-stage object detection,” IEEE Trans. Image Process., 
vol. 30, pp. 9456–9469, 2021. 
[11] L. Yang, Y. Xu, S. Wang, C. Yuan, Z. Zhang, B. Li, and W. Hu, “Pdnet: 
Toward better one-stage object detection with prediction decoupling,” 
IEEE Trans. Image Process., vol. 31, pp. 5121–5133, 2022. 
[12] L. Hou, K. Lu, and J. Xue, “Refined one-stage oriented object detection 
method for remote sensing images,” IEEE Trans. Image Process., 
vol. 31, pp. 1545–1558, 2022. 
[13] P.-Y. Chen, M.-C. Chang, J.-W. Hsieh, and Y.-S. Chen, “Parallel residual 
bi-fusion feature pyramid network for accurate single-shot object 
detection,” IEEE Trans. Image Process., vol. 30, pp. 9099–9111, 2021. 
[14] H. Wang, Q. Wang, H. Zhang, Q. Hu, and W. Zuo, “Crabnet: Fully taskspecific 
feature learning for one-stage object detection,” IEEE Trans. 
Image Process., vol. 31, pp. 2962–2974, 2022. 
[15] R. Girshick, J. Donahue, T. Darrell, and J. Malik, “Rich feature 
hierarchies for accurate object detection and semantic segmentation,” 
in Proc. IEEE Conf. Comp. Vis. Patt. Recogn., 2014, pp. 580–587. 
[16] S. Ren, K. He, R. Girshick, and J. Sun, “Faster r-cnn: Towards real-time 
object detection with region proposal networks,” Advances in neural 
information processing systems, vol. 28, 2015. 
[17] K. He, G. Gkioxari, P. Doll´ar, and R. Girshick, “Mask r-cnn,” in Proc. 
IEEE Int. Conf. Comp. Vis., 2017, pp. 2961–2969. 
[18] J. Redmon and A. Farhadi, “Yolo9000: better, faster, stronger,” in Proc. 
IEEE Conf. Comp. Vis. Patt. Recogn., 2017, pp. 7263–7271. 
IEEE TRANSACTIONS ON IMAGE PROCESSING 13 
[19] J. Redmon, S. Divvala, R. Girshick, and A. Farhadi, “You only look 
once: Unified, real-time object detection,” in Proc. IEEE Conf. Comp. 
Vis. Patt. Recogn., 2016, pp. 779–788. 
[20] C.-Y. Wang, A. Bochkovskiy, and H.-Y. M. Liao, “Yolov7: Trainable 
bag-of-freebies sets new state-of-the-art for real-time object detectors,” 
in Proc. IEEE Conf. Comp. Vis. Patt. Recogn., 2023, pp. 7464–7475. 
[21] W. Liu, D. Anguelov, D. Erhan, C. Szegedy, S. Reed, C.-Y. Fu, and 
A. C. Berg, “Ssd: Single shot multibox detector,” in Computer Vision– 
ECCV 2016: 14th European Conference, Amsterdam, The Netherlands, 
October 11–14, 2016, Proceedings, Part I 14, 2016, pp. 21–37. 
[22] T.-Y. Lin, P. Goyal, R. Girshick, K. He, and P. Doll´ar, “Focal loss for 
dense object detection,” in Proc. IEEE Int. Conf. Comp. Vis., 2017, pp. 
2980–2988. 
[23] N. Carion, F. Massa, G. Synnaeve, N. Usunier, A. Kirillov, and 
S. Zagoruyko, “End-to-end object detection with transformers,” in Proc. 
Eur. Conf. Comp. Vis. Springer, 2020, pp. 213–229. 
[24] X. Zhu, W. Su, L. Lu, B. Li, X. Wang, and J. Dai, “Deformable detr: 
Deformable transformers for end-to-end object detection,” arXiv preprint 
arXiv:2010.04159, 2020. 
[25] Z. Liu, Y. Lin, Y. Cao, H. Hu, Y. Wei, Z. Zhang, S. Lin, and 
B. Guo, “Swin transformer: Hierarchical vision transformer using shifted 
windows,” in Proc. IEEE Int. Conf. Comp. Vis., 2021, pp. 10 012–10 022. 
[26] A. Dosovitskiy, L. Beyer, A. Kolesnikov, D. Weissenborn, X. Zhai, 
T. Unterthiner, M. Dehghani, M. Minderer, G. Heigold, S. Gelly, 
J. Uszkoreit, and N. Houlsby, “An image is worth 16x16 words: 
Transformers for image recognition at scale,” in Proc. Int. Conf. Learn. 
Representations, 2021. 
[27] T. M. Moerland, J. Broekens, A. Plaat, C. M. Jonker et al., “Modelbased 
reinforcement learning: A survey,” Foundations and Trends® in 
Machine Learning, vol. 16, no. 1, pp. 1–118, 2023. 
[28] G. Scarpellini, S. Rosa, P. Morerio, L. Natale, and A. Del Bue, “Look 
around and learn: self-improving object detection by exploration,” arXiv 
preprint arXiv:2302.03566, 2023. 
[29] D. S. Chaplot, M. Dalal, S. Gupta, J. Malik, and R. R. Salakhutdinov, 
“Seal: Self-supervised embodied active learning using exploration and 
3d consistency,” Advances in neural information processing systems, 
vol. 34, pp. 13 086–13 098, 2021. 
[30] J. C. Caicedo and S. Lazebnik, “Active object localization with deep 
reinforcement learning,” in Proc. IEEE Int. Conf. Comp. Vis., 2015, pp. 
2488–2496. 
[31] V. Mnih, K. Kavukcuoglu, D. Silver, A. Graves, I. Antonoglou, D. Wierstra, 
and M. Riedmiller, “Playing atari with deep reinforcement learning,” 
arXiv preprint arXiv:1312.5602, 2013. 
[32] M. Bellver, X. Gir´o-i Nieto, F. Marqu´es, and J. Torres, “Hierarchical 
object detection with deep reinforcement learning,” arXiv preprint 
arXiv:1611.03718, 2016. 
[33] Y. Wang, L. Zhang, L. Wang, and Z. Wang, “Multitask learning for 
object localization with deep reinforcement learning,” IEEE Trans. 
Cogn. Devel. Syst., vol. 11, no. 4, pp. 573–580, 2018. 
[34] Z. Jie, X. Liang, J. Feng, X. Jin, W. Lu, and S. Yan, “Tree-structured 
reinforcement learning for sequential object localization,” Advances in 
neural information processing systems, vol. 29, 2016. 
[35] A. Pirinen and C. Sminchisescu, “Deep reinforcement learning of region 
proposal networks for object detection,” in Proc. IEEE Conf. Comp. Vis. 
Patt. Recogn., 2018, pp. 6945–6954. 
[36] M. Zhou, L. Liu, and R. Wang, “Reinforcedet: Object detection by 
integrating reinforcement learning with decoupled pipeline.” in Proc. 
IEEE Int. Conf. Image Process., 2021, pp. 2778–2782. 
[37] Y. Chen, Y. Wu, and L. Liu, “Deep reinforcement learning for object detection 
based on improved reward mechanism,” (in Chinese), Computer 
Systems Applications, vol. 33, no. 10, p. 106, 2024. 
[38] V. Mnih, K. Kavukcuoglu, D. Silver, A. A. Rusu, J. Veness, M. G. 
Bellemare, A. Graves, M. Riedmiller, A. K. Fidjeland, G. Ostrovski 
et al., “Human-level control through deep reinforcement learning,” 
nature, vol. 518, no. 7540, pp. 529–533, 2015. 
[39] K. Simonyan and A. Zisserman, “Very deep convolutional networks for 
large-scale image recognition,” arXiv preprint arXiv:1409.1556, 2014. 
[40] C. Szegedy, V. Vanhoucke, S. Ioffe, J. Shlens, and Z. Wojna, “Rethinking 
the inception architecture for computer vision,” in Proc. IEEE Conf. 
Comp. Vis. Patt. Recogn., 2016, pp. 2818–2826. 
[41] K. He, X. Zhang, S. Ren, and J. Sun, “Deep residual learning for image 
recognition,” in Proc. IEEE Conf. Comp. Vis. Patt. Recogn., 2016, pp. 
770–778. 
[42] G. Huang, Z. Liu, L. Van Der Maaten, and K. Q. Weinberger, “Densely 
connected convolutional networks,” in Proc. IEEE Conf. Comp. Vis. Patt. 
Recogn., 2017, pp. 4700–4708. 
[43] X. Ding, X. Zhang, N. Ma, J. Han, G. Ding, and J. Sun, “Repvgg: 
Making vgg-style convnets great again,” in Proc. IEEE Conf. Comp. 
Vis. Patt. Recogn., 2021, pp. 13 733–13 742. 
[44] M. Everingham, L. Van Gool, C. K. Williams, J. Winn, and A. Zisserman, 
“The pascal visual object classes (voc) challenge,” Int. J. Comput. 
Vision, vol. 88, pp. 303–338, 2010. 
[45] Q.-x. Zhang, G.-h. Lin, Y.-m. Zhang, G. Xu, and J.-j. Wang, “Wildland 
forest fire smoke detection based on faster r-cnn using synthetic smoke 
images,” Procedia engineering, vol. 211, pp. 441–446, 2018. 
[46] Z. Liu, H. Mao, C.-Y. Wu, C. Feichtenhofer, T. Darrell, and S. Xie, “A 
convnet for the 2020s,” in Proc. IEEE Conf. Comp. Vis. Patt. Recogn., 
2022, pp. 11 976–11 986. 
[47] L. Zhang, C. Lu, H. Xu, A. Chen, L. Li, and G. Zhou, “Mmfnet: Forest 
fire smoke detection using multiscale convergence coordinated pyramid 
network with mixed attention and fast-robust nms,” IEEE Internet Things 
J., vol. 10, no. 20, pp. 18 168–18 180, 2023. 
[48] J. Zhan, Y. Hu, G. Zhou, Y. Wang, W. Cai, and L. Li, “A high-precision 
forest fire smoke detection approach based on argnet,” Computers and 
Electronics in Agriculture, vol. 196, p. 106874, 2022. 
[49] J. Li, G. Zhou, A. Chen, C. Lu, and L. Li, “Bcmnet: Cross-layer extraction 
structure and multiscale downsampling network with bidirectional 
transpose fpn for fast detection of wildfire smoke,” IEEE Syst. J., vol. 17, 
no. 1, pp. 1235–1246, 2023. 
[50] Y. Hu, J. Zhan, G. Zhou, A. Chen, W. Cai, K. Guo, Y. Hu, and L. Li, 
“Fast forest fire smoke detection using mvmnet,” Knowledge-Based 
Systems, vol. 241, p. 108219, 2022. 
[51] J. Li, G. Zhou, A. Chen, Y. Wang, J. Jiang, Y. Hu, and C. Lu, “Adaptive 
linear feature-reuse network for rapid forest fire smoke detection model,” 
Ecological Informatics, vol. 68, p. 101584, 2022. 
[52] C. Lyu, W. Zhang, H. Huang, Y. Zhou, Y. Wang, Y. Liu, S. Zhang, and 
K. Chen, “Rtmdet: An empirical study of designing real-time object 
detectors,” arXiv preprint arXiv:2212.07784, 2022. 
Xun Zhou received the B. Admin. degree in logistics 
management from Central South University 
of Forestry and Technology, Changsha, China, in 
2022. He is currently pursuing the M.Sc. degree 
in electronic information with the Central South 
University of Forestry and Technology, Changsha, 
China. 
His main research interests include deep reinforcement 
learning and image processing. 
Guangjie Han (Fellow, IEEE) is currently a Professor 
with the Department of Internet of Things 
Engineering, Hohai University, Changzhou, China. 
He received his Ph.D. degree from Northeastern University, 
Shenyang, China, in 2004. In February 2008, 
he finished his work as a Postdoctoral Researcher 
with the Department of Computer Science, Chonnam 
National University, Gwangju, Korea. From October 
2010 to October 2011, he was a Visiting Research 
Scholar with Osaka University, Suita, Japan. From 
January 2017 to February 2017, he was a Visiting 
Professor with City University of Hong Kong, China. From July 2017 to July 
2020, he was a Distinguished Professor with Dalian University of Technology, 
China. His current research interests include Internet of Things, Industrial 
Internet, Machine Learning and Artificial Intelligence, Mobile Computing, 
Security and Privacy. Dr. Han has over 500 peer-reviewed journal and 
conference papers, in addition to 160 granted and pending patents. Currently, 
his H-index is 73 and i10-index is 328 in Google Citation (Google Scholar). 
The total citation count of his papers raises above 19200+ times. 
Dr. Han is a Fellow of the UK Institution of Engineering and Technology 
(FIET). He has served on the Editorial Boards of up to 10 international 
journals, including the IEEE TII, IEEE TCCN, IEEE TVT, IEEE Systems, etc. 
He has guest-edited several special issues in IEEE Journals and Magazines, 
including the IEEE JSAC, IEEE Communications, IEEE Wireless Communications, 
Computer Networks, etc. Dr. Han has also served as chair of organizing 
and technical committees in many international conferences. He has been 
awarded 2020 IEEE Systems Journal Annual Best Paper Award and the 2017- 
2019 IEEE ACCESS Outstanding Associate Editor Award. He is a Fellow of 
IEEE. 
IEEE TRANSACTIONS ON IMAGE PROCESSING 14 
Guoxiong Zhou received the Ph.D. degree in control 
science and engineering from Central South University, 
Changsha, China, in 2010. 
He is currently a Professor with the Central South 
University of Forestry and Technology, Changsha. 
His research interests include forest fire prevention 
and robotics. 
Yongfei Xue received the Ph.D. degree in control 
science and engineering with the School of Automation, 
Central South University, Changsha, China, 
in 2021.He is currently a lecturer with the Central 
South University of Forestry and Technology, 
Changsha, China. 
His research interests include modeling, optimization, 
control and artificial intelligence. 
Mingjie Lv (Graduate Student Member, IEEE) is 
currently pursuing the Ph.D. degree with the School 
of Automation, Central South University, Changsha, 
China. 
His research interests include graph networks, 
industrial intelligence, pattern recognition, and machine 
learning. 
Aibin Chen received the Ph.D. degree in computer 
application technology from Central South University, 
Changsha, China, in 2010. 
He is currently a Professor with the Central South 
University of Forestry and Technology, Changsha. 
His main research interests include artificial intelligence 
and forest information engineering.