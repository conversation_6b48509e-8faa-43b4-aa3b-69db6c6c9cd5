% Generated by IEEEtran.bst, version: 1.14 (2015/08/26)
\begin{thebibliography}{1}
\providecommand{\url}[1]{#1}
\csname url@samestyle\endcsname
\providecommand{\newblock}{\relax}
\providecommand{\bibinfo}[2]{#2}
\providecommand{\BIBentrySTDinterwordspacing}{\spaceskip=0pt\relax}
\providecommand{\BIBentryALTinterwordstretchfactor}{4}
\providecommand{\BIBentryALTinterwordspacing}{\spaceskip=\fontdimen2\font plus
\BIBentryALTinterwordstretchfactor\fontdimen3\font minus
  \fontdimen4\font\relax}
\providecommand{\BIBforeignlanguage}[2]{{%
\expandafter\ifx\csname l@#1\endcsname\relax
\typeout{** WARNING: IEEEtran.bst: No hyphenation pattern has been}%
\typeout{** loaded for the language `#1'. Using the pattern for}%
\typeout{** the default language instead.}%
\else
\language=\csname l@#1\endcsname
\fi
#2}}
\providecommand{\BIBdecl}{\relax}
\BIBdecl

\bibitem{ren2015faster}
S.~<PERSON>, <PERSON><PERSON>~<PERSON>, <PERSON><PERSON>~<PERSON><PERSON><PERSON><PERSON>, and J.~Sun, ``Faster r-cnn: Towards real-time
  object detection with region proposal networks,'' in \emph{Advances in neural
  information processing systems}, 2015, pp. 91--99.

\bibitem{redmon2016you}
J.~Redmon, S.~Divvala, R.~Girshick, and A.~Farhadi, ``You only look once:
  Unified, real-time object detection,'' in \emph{Proceedings of the IEEE
  conference on computer vision and pattern recognition}, 2016, pp. 779--788.

\bibitem{caicedo2015active}
J.~C. Caicedo and S.~Lazebnik, ``Active object localization with deep
  reinforcement learning,'' in \emph{Proceedings of the IEEE international
  conference on computer vision}, 2015, pp. 2488--2496.

\bibitem{uzkent2020efficient}
B.~Uzkent, C.~Yeh, and S.~Ermon, ``Efficient object detection in large images
  using deep reinforcement learning,'' in \emph{Proceedings of the IEEE/CVF
  Winter Conference on Applications of Computer Vision}, 2020, pp. 1824--1833.

\bibitem{ba2014multiple}
J.~Ba, V.~Mnih, and K.~Kavukcuoglu, ``Multiple object recognition with visual
  attention,'' \emph{arXiv preprint arXiv:1412.7755}, 2014.

\bibitem{rahimi2007random}
A.~Rahimi and B.~Recht, ``Random features for large-scale kernel machines,'' in
  \emph{Advances in neural information processing systems}, 2007, pp.
  1177--1184.

\bibitem{strukov2008missing}
D.~B. Strukov, G.~S. Snider, D.~R. Stewart, and R.~S. Williams, ``The missing
  memristor found,'' \emph{nature}, vol. 453, no. 7191, pp. 80--83, 2008.

\bibitem{prezioso2015training}
M.~Prezioso, F.~Merrikh-Bayat, B.~D. Hoskins, G.~C. Adam, K.~K. Likharev, and
  D.~B. Strukov, ``Training and operation of an integrated neuromorphic network
  based on metal-oxide memristors,'' \emph{Nature}, vol. 521, no. 7550, pp.
  61--64, 2015.

\end{thebibliography}
